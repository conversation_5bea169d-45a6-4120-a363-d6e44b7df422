const { MongoClient } = require('mongodb');

const uri = 'mongodb+srv://saurabhdahariya83:<EMAIL>/productfinder?retryWrites=true&w=majority&appName=Cluster0';

const devices = [
  {
    name: 'iPhone 15 Pro',
    brand: 'Apple',
    type: 'smartphone',
    specs: {
      bluetooth: '5.3',
      supportedCodecs: ['AAC', 'SBC'],
      usbType: 'Type-C',
      fastCharging: true,
      maxChargingPower: 27
    },
    imageUrl: 'https://example.com/iphone15pro.jpg'
  },
  {
    name: 'Galaxy S24 Ultra',
    brand: 'Samsung',
    type: 'smartphone',
    specs: {
      bluetooth: '5.3',
      supportedCodecs: ['AAC', 'SBC', 'LDAC', 'Samsung Scalable'],
      usbType: 'Type-C',
      fastCharging: true,
      maxChargingPower: 45
    },
    imageUrl: 'https://example.com/galaxys24.jpg'
  },
  {
    name: 'MacBook Pro M3',
    brand: 'Apple',
    type: 'laptop',
    specs: {
      bluetooth: '5.3',
      supportedCodecs: ['AAC', 'SBC'],
      ports: ['USB-C', 'Thunderbolt 4'],
      storageInterface: 'NVMe'
    },
    imageUrl: 'https://example.com/macbookpro.jpg'
  },
  {
    name: 'ThinkPad X1 Carbon',
    brand: 'Lenovo',
    type: 'laptop',
    specs: {
      bluetooth: '5.1',
      supportedCodecs: ['AAC', 'SBC'],
      ports: ['USB-C', 'USB-A', 'HDMI'],
      storageInterface: 'NVMe'
    },
    imageUrl: 'https://example.com/thinkpad.jpg'
  },
  {
    name: 'iPad Pro',
    brand: 'Apple',
    type: 'tablet',
    specs: {
      bluetooth: '5.3',
      supportedCodecs: ['AAC', 'SBC'],
      usbType: 'Type-C',
      fastCharging: true
    },
    imageUrl: 'https://example.com/ipadpro.jpg'
  }
];

const accessories = [
  {
    name: 'AirPods Pro 2',
    brand: 'Apple',
    type: 'tws',
    specs: {
      supportedCodecs: ['AAC', 'SBC'],
      features: ['ANC', 'Spatial Audio', 'Transparency Mode'],
      batteryLife: 30,
      chargingCase: 'Lightning'
    },
    price: { amount: 249, currency: 'USD' },
    imageUrl: 'https://example.com/airpodspro2.jpg',
    buyLinks: {
      amazon: 'https://amazon.com/airpods-pro-2',
      flipkart: 'https://flipkart.com/airpods-pro-2'
    }
  },
  {
    name: 'WF-1000XM4',
    brand: 'Sony',
    type: 'tws',
    specs: {
      supportedCodecs: ['AAC', 'SBC', 'LDAC'],
      features: ['ANC', 'Touch Control', 'Ambient Sound'],
      batteryLife: 24,
      chargingCase: 'Type-C'
    },
    price: { amount: 199, currency: 'USD' },
    imageUrl: 'https://example.com/sonywf1000xm4.jpg',
    buyLinks: {
      amazon: 'https://amazon.com/sony-wf-1000xm4',
      flipkart: 'https://flipkart.com/sony-wf-1000xm4'
    }
  },
  {
    name: '980 PRO',
    brand: 'Samsung',
    type: 'ssd',
    specs: {
      storageInterface: 'NVMe',
      capacity: '1TB',
      readSpeed: 7000,
      writeSpeed: 5000,
      formFactor: 'M.2 2280'
    },
    price: { amount: 129, currency: 'USD' },
    imageUrl: 'https://example.com/samsung980pro.jpg',
    buyLinks: {
      amazon: 'https://amazon.com/samsung-980-pro',
      flipkart: 'https://flipkart.com/samsung-980-pro'
    }
  },
  {
    name: 'USB-C Charger 65W',
    brand: 'Anker',
    type: 'charger',
    specs: {
      powerOutput: 65,
      cableType: 'Type-C',
      fastCharging: true,
      protocols: ['PD 3.0', 'QC 3.0']
    },
    price: { amount: 39, currency: 'USD' },
    imageUrl: 'https://example.com/ankercharger.jpg',
    buyLinks: {
      amazon: 'https://amazon.com/anker-65w-charger',
      flipkart: 'https://flipkart.com/anker-65w-charger'
    }
  },
  {
    name: 'Studio Display',
    brand: 'Apple',
    type: 'monitor',
    specs: {
      resolution: '5K',
      refreshRate: [60],
      connectivity: ['Thunderbolt 3', 'USB-C'],
      size: 27
    },
    price: { amount: 1599, currency: 'USD' },
    imageUrl: 'https://example.com/studiodisplay.jpg',
    buyLinks: {
      amazon: 'https://amazon.com/apple-studio-display',
      flipkart: 'https://flipkart.com/apple-studio-display'
    }
  }
];

async function seedDatabase() {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB Atlas');
    
    const db = client.db('productfinder');
    
    // Clear existing data
    await db.collection('devices').deleteMany({});
    await db.collection('accessories').deleteMany({});
    
    // Insert devices
    const deviceResult = await db.collection('devices').insertMany(devices);
    console.log(`Inserted ${deviceResult.insertedCount} devices`);
    
    // Insert accessories
    const accessoryResult = await db.collection('accessories').insertMany(accessories);
    console.log(`Inserted ${accessoryResult.insertedCount} accessories`);
    
    // Create indexes for better performance
    await db.collection('devices').createIndex({ type: 1, brand: 1 });
    await db.collection('accessories').createIndex({ type: 1, brand: 1 });
    
    console.log('Database seeded successfully!');
    
  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    await client.close();
  }
}

seedDatabase();
