'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Select } from '@/components/ui/Select';

export default function AdminPage() {
  const [activeTab, setActiveTab] = useState<'device' | 'accessory'>('device');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const [deviceForm, setDeviceForm] = useState({
    name: '',
    brand: '',
    type: 'smartphone',
    bluetooth: '',
    supportedCodecs: '',
    usbType: '',
    powerDelivery: '',
    storageInterface: '',
    displayPorts: '',
    hdmiVersion: '',
    refreshRateSupport: '',
    releaseYear: new Date().getFullYear()
  });

  const [accessoryForm, setAccessoryForm] = useState({
    name: '',
    brand: '',
    type: 'tws',
    supportedCodecs: '',
    features: '',
    storageInterface: '',
    capacity: '',
    powerOutput: '',
    powerDelivery: '',
    cableType: '',
    resolution: '',
    refreshRate: '',
    hdmiVersion: '',
    audioSupport: '',
    price: '',
    currency: 'USD',
    deviceTypes: '',
    minBluetoothVersion: '',
    requiredPorts: ''
  });

  const handleDeviceSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    try {
      const deviceData = {
        ...deviceForm,
        specs: {
          bluetooth: deviceForm.bluetooth || undefined,
          supportedCodecs: deviceForm.supportedCodecs ? deviceForm.supportedCodecs.split(',').map(s => s.trim()) : undefined,
          usbType: deviceForm.usbType || undefined,
          powerDelivery: deviceForm.powerDelivery || undefined,
          storageInterface: deviceForm.storageInterface ? deviceForm.storageInterface.split(',').map(s => s.trim()) : undefined,
          displayPorts: deviceForm.displayPorts ? deviceForm.displayPorts.split(',').map(s => s.trim()) : undefined,
          hdmiVersion: deviceForm.hdmiVersion || undefined,
          refreshRateSupport: deviceForm.refreshRateSupport ? deviceForm.refreshRateSupport.split(',').map(s => parseInt(s.trim())) : undefined
        }
      };

      const response = await fetch('/api/devices', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(deviceData)
      });

      if (response.ok) {
        setMessage('Device added successfully!');
        setDeviceForm({
          name: '', brand: '', type: 'smartphone', bluetooth: '', supportedCodecs: '',
          usbType: '', powerDelivery: '', storageInterface: '', displayPorts: '',
          hdmiVersion: '', refreshRateSupport: '', releaseYear: new Date().getFullYear()
        });
      } else {
        setMessage('Error adding device');
      }
    } catch (error) {
      setMessage('Error adding device');
    } finally {
      setLoading(false);
    }
  };

  const handleAccessorySubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    try {
      const accessoryData = {
        ...accessoryForm,
        specs: {
          supportedCodecs: accessoryForm.supportedCodecs ? accessoryForm.supportedCodecs.split(',').map(s => s.trim()) : undefined,
          features: accessoryForm.features ? accessoryForm.features.split(',').map(s => s.trim()) : undefined,
          storageInterface: accessoryForm.storageInterface || undefined,
          capacity: accessoryForm.capacity || undefined,
          powerOutput: accessoryForm.powerOutput || undefined,
          powerDelivery: accessoryForm.powerDelivery || undefined,
          cableType: accessoryForm.cableType || undefined,
          resolution: accessoryForm.resolution || undefined,
          refreshRate: accessoryForm.refreshRate ? accessoryForm.refreshRate.split(',').map(s => parseInt(s.trim())) : undefined,
          hdmiVersion: accessoryForm.hdmiVersion || undefined,
          audioSupport: accessoryForm.audioSupport ? accessoryForm.audioSupport.split(',').map(s => s.trim()) : undefined
        },
        compatibility: {
          deviceTypes: accessoryForm.deviceTypes ? accessoryForm.deviceTypes.split(',').map(s => s.trim()) : [],
          requirements: {
            minBluetoothVersion: accessoryForm.minBluetoothVersion || undefined,
            requiredPorts: accessoryForm.requiredPorts ? accessoryForm.requiredPorts.split(',').map(s => s.trim()) : undefined
          }
        },
        price: accessoryForm.price ? {
          amount: parseFloat(accessoryForm.price),
          currency: accessoryForm.currency
        } : undefined
      };

      const response = await fetch('/api/accessories', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(accessoryData)
      });

      if (response.ok) {
        setMessage('Accessory added successfully!');
        setAccessoryForm({
          name: '', brand: '', type: 'tws', supportedCodecs: '', features: '',
          storageInterface: '', capacity: '', powerOutput: '', powerDelivery: '',
          cableType: '', resolution: '', refreshRate: '', hdmiVersion: '',
          audioSupport: '', price: '', currency: 'USD', deviceTypes: '',
          minBluetoothVersion: '', requiredPorts: ''
        });
      } else {
        setMessage('Error adding accessory');
      }
    } catch (error) {
      setMessage('Error adding accessory');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <h1 className="text-3xl font-bold text-gray-900">Admin Panel</h1>
          <p className="mt-2 text-gray-600">Add devices and accessories to the database</p>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Tab Navigation */}
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg mb-8">
          <button
            onClick={() => setActiveTab('device')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'device'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Add Device
          </button>
          <button
            onClick={() => setActiveTab('accessory')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'accessory'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Add Accessory
          </button>
        </div>

        {message && (
          <div className={`mb-6 p-4 rounded-lg ${
            message.includes('successfully') ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
          }`}>
            {message}
          </div>
        )}

        {/* Device Form */}
        {activeTab === 'device' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-6">Add New Device</h2>
            <form onSubmit={handleDeviceSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Name *</label>
                  <input
                    type="text"
                    required
                    value={deviceForm.name}
                    onChange={(e) => setDeviceForm({...deviceForm, name: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Brand *</label>
                  <input
                    type="text"
                    required
                    value={deviceForm.brand}
                    onChange={(e) => setDeviceForm({...deviceForm, brand: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <Select
                  label="Type *"
                  value={deviceForm.type}
                  onChange={(e) => setDeviceForm({...deviceForm, type: e.target.value})}
                  options={[
                    { value: 'smartphone', label: 'Smartphone' },
                    { value: 'laptop', label: 'Laptop' },
                    { value: 'tablet', label: 'Tablet' }
                  ]}
                />
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Bluetooth Version</label>
                  <input
                    type="text"
                    placeholder="e.g., 5.3"
                    value={deviceForm.bluetooth}
                    onChange={(e) => setDeviceForm({...deviceForm, bluetooth: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Supported Codecs</label>
                  <input
                    type="text"
                    placeholder="e.g., AAC, SBC, LDAC"
                    value={deviceForm.supportedCodecs}
                    onChange={(e) => setDeviceForm({...deviceForm, supportedCodecs: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">USB Type</label>
                  <input
                    type="text"
                    placeholder="e.g., Type-C"
                    value={deviceForm.usbType}
                    onChange={(e) => setDeviceForm({...deviceForm, usbType: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
              <Button type="submit" loading={loading} className="w-full">
                Add Device
              </Button>
            </form>
          </div>
        )}

        {/* Accessory Form */}
        {activeTab === 'accessory' && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-6">Add New Accessory</h2>
            <form onSubmit={handleAccessorySubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Name *</label>
                  <input
                    type="text"
                    required
                    value={accessoryForm.name}
                    onChange={(e) => setAccessoryForm({...accessoryForm, name: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Brand *</label>
                  <input
                    type="text"
                    required
                    value={accessoryForm.brand}
                    onChange={(e) => setAccessoryForm({...accessoryForm, brand: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <Select
                  label="Type *"
                  value={accessoryForm.type}
                  onChange={(e) => setAccessoryForm({...accessoryForm, type: e.target.value})}
                  options={[
                    { value: 'tws', label: 'TWS Earbuds' },
                    { value: 'ssd', label: 'SSD' },
                    { value: 'charger', label: 'Charger' },
                    { value: 'cable', label: 'Cable' },
                    { value: 'monitor', label: 'Monitor' },
                    { value: 'soundbar', label: 'Soundbar' }
                  ]}
                />
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Price</label>
                  <input
                    type="number"
                    step="0.01"
                    placeholder="e.g., 199.99"
                    value={accessoryForm.price}
                    onChange={(e) => setAccessoryForm({...accessoryForm, price: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Compatible Device Types</label>
                  <input
                    type="text"
                    placeholder="e.g., smartphone, laptop"
                    value={accessoryForm.deviceTypes}
                    onChange={(e) => setAccessoryForm({...accessoryForm, deviceTypes: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Supported Codecs</label>
                  <input
                    type="text"
                    placeholder="e.g., AAC, SBC, LDAC"
                    value={accessoryForm.supportedCodecs}
                    onChange={(e) => setAccessoryForm({...accessoryForm, supportedCodecs: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
              <Button type="submit" loading={loading} className="w-full">
                Add Accessory
              </Button>
            </form>
          </div>
        )}
      </main>
    </div>
  );
}
