'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Select } from './ui/Select';
import { Combobox, ComboboxOption } from './ui/Combobox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/Card';
import { Badge } from './ui/Badge';
import { Smartphone, Laptop, Tablet, Search } from 'lucide-react';

interface Device {
  _id: string;
  name: string;
  brand: string;
  type: string;
  specs: {
    bluetooth?: string;
    supportedCodecs?: string[];
  };
  imageUrl?: string;
}

interface DeviceSelectorProps {
  onDeviceSelect: (device: Device | null) => void;
  selectedDevice: Device | null;
}

export const DeviceSelector: React.FC<DeviceSelectorProps> = ({
  onDeviceSelect,
  selectedDevice
}) => {
  const [deviceType, setDeviceType] = useState<string>('');
  const [devices, setDevices] = useState<Device[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState<string>('');

  const deviceTypes = [
    { value: 'smartphone', label: 'Smartphone', icon: Smartphone },
    { value: 'laptop', label: 'Laptop', icon: Laptop },
    { value: 'tablet', label: 'Tablet', icon: Tablet }
  ];

  useEffect(() => {
    if (deviceType) {
      fetchDevices(deviceType);
    } else {
      setDevices([]);
      onDeviceSelect(null);
    }
  }, [deviceType]);

  const fetchDevices = async (type: string, search?: string) => {
    setLoading(true);
    try {
      let url = `/api/devices?type=${type}`;
      if (search) {
        url += `&search=${encodeURIComponent(search)}`;
      }
      const response = await fetch(url);
      const data = await response.json();
      setDevices(data.devices || []);
    } catch (error) {
      console.error('Error fetching devices:', error);
    } finally {
      setLoading(false);
    }
  };

  // Debounced search
  useEffect(() => {
    if (deviceType) {
      const timeoutId = setTimeout(() => {
        fetchDevices(deviceType, searchQuery);
      }, 300);
      return () => clearTimeout(timeoutId);
    }
  }, [searchQuery, deviceType]);

  const handleDeviceChange = (deviceId: string) => {
    const device = devices.find(d => d._id === deviceId) || null;
    onDeviceSelect(device);
  };

  const deviceOptions: ComboboxOption[] = devices.map(device => ({
    value: device._id,
    label: `${device.brand} ${device.name}`,
    description: `${device.type} • ${device.specs.bluetooth ? `Bluetooth ${device.specs.bluetooth}` : 'No Bluetooth info'}`
  }));

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="space-y-6"
    >
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center">
          <Search className="mr-2" size={20} />
          Select Your Device
        </CardTitle>
        <CardDescription>
          Choose your smartphone, laptop, or tablet to check compatibility
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Device Type Selection */}
        <div className="grid grid-cols-3 gap-3">
          {deviceTypes.map(({ value, label, icon: Icon }) => (
            <motion.button
              key={value}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => {
                setDeviceType(value);
                setSearchQuery('');
                onDeviceSelect(null);
              }}
              className={`p-4 border rounded-lg flex flex-col items-center space-y-2 transition-all ${
                deviceType === value
                  ? 'border-blue-500 bg-blue-50 text-blue-700 shadow-md'
                  : 'border-gray-300 hover:border-gray-400 hover:shadow-sm'
              }`}
            >
              <Icon size={24} />
              <span className="text-sm font-medium">{label}</span>
            </motion.button>
          ))}
        </div>

        {/* Search and Device Selection */}
        {deviceType && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Search for your device
              </label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <input
                  type="text"
                  placeholder={`Search ${deviceType}s...`}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            {devices.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select your device
                </label>
                <Combobox
                  options={deviceOptions}
                  value={selectedDevice?._id || ''}
                  onValueChange={handleDeviceChange}
                  placeholder="Choose your device..."
                  searchPlaceholder="Search devices..."
                  emptyText="No devices found. Try a different search term."
                />
              </div>
            )}
          </div>
        )}

        {loading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex items-center justify-center py-8"
          >
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-sm text-gray-600">Loading devices...</span>
          </motion.div>
        )}

        {/* Selected Device Info */}
        {selectedDevice && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200"
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h4 className="font-semibold text-gray-900 mb-2">
                  {selectedDevice.brand} {selectedDevice.name}
                </h4>
                <div className="flex flex-wrap gap-2">
                  <Badge variant="secondary" className="text-xs">
                    {selectedDevice.type}
                  </Badge>
                  {selectedDevice.specs.bluetooth && (
                    <Badge variant="outline" className="text-xs">
                      Bluetooth {selectedDevice.specs.bluetooth}
                    </Badge>
                  )}
                  {selectedDevice.specs.supportedCodecs && selectedDevice.specs.supportedCodecs.length > 0 && (
                    <Badge variant="outline" className="text-xs">
                      {selectedDevice.specs.supportedCodecs.length} Audio Codecs
                    </Badge>
                  )}
                </div>
                {selectedDevice.specs.supportedCodecs && (
                  <p className="text-xs text-gray-600 mt-2">
                    Codecs: {selectedDevice.specs.supportedCodecs.join(', ')}
                  </p>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </CardContent>
    </motion.div>
  );
};
