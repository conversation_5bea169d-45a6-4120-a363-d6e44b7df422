'use client';

import React, { useState, useEffect } from 'react';
import { Select } from './ui/Select';
import { Smartphone, Laptop, Tablet } from 'lucide-react';

interface Device {
  _id: string;
  name: string;
  brand: string;
  type: string;
  specs: {
    bluetooth?: string;
    supportedCodecs?: string[];
  };
  imageUrl?: string;
}

interface DeviceSelectorProps {
  onDeviceSelect: (device: Device | null) => void;
  selectedDevice: Device | null;
}

export const DeviceSelector: React.FC<DeviceSelectorProps> = ({
  onDeviceSelect,
  selectedDevice
}) => {
  const [deviceType, setDeviceType] = useState<string>('');
  const [devices, setDevices] = useState<Device[]>([]);
  const [loading, setLoading] = useState(false);

  const deviceTypes = [
    { value: 'smartphone', label: 'Smartphone', icon: Smartphone },
    { value: 'laptop', label: 'Laptop', icon: Laptop },
    { value: 'tablet', label: 'Tablet', icon: Tablet }
  ];

  useEffect(() => {
    if (deviceType) {
      fetchDevices(deviceType);
    } else {
      setDevices([]);
      onDeviceSelect(null);
    }
  }, [deviceType]);

  const fetchDevices = async (type: string) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/devices?type=${type}`);
      const data = await response.json();
      setDevices(data.devices || []);
    } catch (error) {
      console.error('Error fetching devices:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeviceChange = (deviceId: string) => {
    const device = devices.find(d => d._id === deviceId) || null;
    onDeviceSelect(device);
  };

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-3">
          Select Your Device
        </h3>
        
        {/* Device Type Selection */}
        <div className="grid grid-cols-3 gap-3 mb-4">
          {deviceTypes.map(({ value, label, icon: Icon }) => (
            <button
              key={value}
              onClick={() => setDeviceType(value)}
              className={`p-4 border rounded-lg flex flex-col items-center space-y-2 transition-colors ${
                deviceType === value
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
            >
              <Icon size={24} />
              <span className="text-sm font-medium">{label}</span>
            </button>
          ))}
        </div>

        {/* Device Selection */}
        {deviceType && (
          <Select
            label="Choose your specific device"
            value={selectedDevice?._id || ''}
            onChange={(e) => handleDeviceChange(e.target.value)}
            options={devices.map(device => ({
              value: device._id,
              label: `${device.brand} ${device.name}`
            }))}
            disabled={loading}
          />
        )}

        {loading && (
          <div className="flex items-center justify-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-sm text-gray-600">Loading devices...</span>
          </div>
        )}

        {/* Selected Device Info */}
        {selectedDevice && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-gray-900">
              {selectedDevice.brand} {selectedDevice.name}
            </h4>
            {selectedDevice.specs.bluetooth && (
              <p className="text-sm text-gray-600">
                Bluetooth: {selectedDevice.specs.bluetooth}
              </p>
            )}
            {selectedDevice.specs.supportedCodecs && (
              <p className="text-sm text-gray-600">
                Audio Codecs: {selectedDevice.specs.supportedCodecs.join(', ')}
              </p>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
