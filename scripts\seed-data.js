const mongoose = require('mongoose');

// Sample devices
const sampleDevices = [
  {
    name: 'iPhone 15 Pro',
    brand: 'Apple',
    type: 'smartphone',
    specs: {
      bluetooth: '5.3',
      supportedCodecs: ['AAC', 'SBC'],
      usbType: 'Type-C',
      dac: 'built-in',
      powerDelivery: '20W'
    },
    releaseYear: 2023
  },
  {
    name: 'Galaxy S24 Ultra',
    brand: 'Samsung',
    type: 'smartphone',
    specs: {
      bluetooth: '5.3',
      supportedCodecs: ['AAC', 'SBC', 'LDAC', 'Samsung Scalable'],
      usbType: 'Type-C',
      dac: 'built-in',
      powerDelivery: '45W'
    },
    releaseYear: 2024
  },
  {
    name: 'MacBook Pro M3',
    brand: 'Apple',
    type: 'laptop',
    specs: {
      bluetooth: '5.3',
      supportedCodecs: ['AAC', 'SBC'],
      storageInterface: ['NVMe'],
      displayPorts: ['Thunderbolt 4', 'HDMI'],
      hdmiVersion: '2.1',
      refreshRateSupport: [60, 120]
    },
    releaseYear: 2023
  },
  {
    name: 'ThinkPad X1 Carbon',
    brand: 'Lenovo',
    type: 'laptop',
    specs: {
      bluetooth: '5.1',
      supportedCodecs: ['AAC', 'SBC'],
      storageInterface: ['NVMe', 'SATA'],
      displayPorts: ['USB-C', 'HDMI'],
      hdmiVersion: '2.0',
      refreshRateSupport: [60]
    },
    releaseYear: 2023
  }
];

// Sample accessories
const sampleAccessories = [
  {
    name: 'AirPods Pro 2',
    brand: 'Apple',
    type: 'tws',
    specs: {
      supportedCodecs: ['AAC', 'SBC'],
      features: ['ANC', 'Spatial Audio', 'Touch Control']
    },
    compatibility: {
      deviceTypes: ['smartphone', 'laptop', 'tablet'],
      requirements: {
        minBluetoothVersion: '5.0'
      }
    },
    price: { amount: 249, currency: 'USD' }
  },
  {
    name: 'WF-1000XM4',
    brand: 'Sony',
    type: 'tws',
    specs: {
      supportedCodecs: ['AAC', 'SBC', 'LDAC'],
      features: ['ANC', 'Touch Control', 'Multipoint']
    },
    compatibility: {
      deviceTypes: ['smartphone', 'laptop', 'tablet'],
      requirements: {
        minBluetoothVersion: '5.0'
      }
    },
    price: { amount: 199, currency: 'USD' }
  },
  {
    name: '980 PRO',
    brand: 'Samsung',
    type: 'ssd',
    specs: {
      storageInterface: 'NVMe',
      capacity: '1TB',
      readSpeed: '7000 MB/s',
      writeSpeed: '5000 MB/s'
    },
    compatibility: {
      deviceTypes: ['laptop'],
      requirements: {
        requiredPorts: ['NVMe']
      }
    },
    price: { amount: 129, currency: 'USD' }
  },
  {
    name: 'MagSafe Charger',
    brand: 'Apple',
    type: 'charger',
    specs: {
      powerOutput: '15W',
      powerDelivery: '20W',
      cableType: 'Type-C'
    },
    compatibility: {
      deviceTypes: ['smartphone'],
      requirements: {
        requiredPorts: ['Type-C']
      }
    },
    price: { amount: 39, currency: 'USD' }
  },
  {
    name: 'Studio Display',
    brand: 'Apple',
    type: 'monitor',
    specs: {
      resolution: '5K',
      refreshRate: [60],
      hdmiVersion: '2.1'
    },
    compatibility: {
      deviceTypes: ['laptop'],
      requirements: {
        requiredPorts: ['Thunderbolt 4', 'USB-C']
      }
    },
    price: { amount: 1599, currency: 'USD' }
  }
];

async function seedDatabase() {
  try {
    // Connect to MongoDB
    await mongoose.connect('mongodb://localhost:27017/productfinder');
    console.log('Connected to MongoDB');

    // Clear existing data
    await mongoose.connection.db.collection('devices').deleteMany({});
    await mongoose.connection.db.collection('accessories').deleteMany({});
    console.log('Cleared existing data');

    // Insert sample data
    await mongoose.connection.db.collection('devices').insertMany(sampleDevices);
    await mongoose.connection.db.collection('accessories').insertMany(sampleAccessories);
    
    console.log('Sample data inserted successfully');
    console.log(`Inserted ${sampleDevices.length} devices and ${sampleAccessories.length} accessories`);

  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    await mongoose.connection.close();
    console.log('Database connection closed');
  }
}

seedDatabase();
