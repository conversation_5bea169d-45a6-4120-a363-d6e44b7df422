import axios from 'axios';
import * as cheerio from 'cheerio';

export interface GSMArenaDevice {
  name: string;
  brand: string;
  bluetooth?: string;
  supportedCodecs?: string[];
  usbType?: string;
  chargingProtocols?: string[];
  dac?: boolean;
  displaySize?: string;
  resolution?: string;
  refreshRate?: number[];
}

export class GSMArenaScraper {
  private static readonly BASE_URL = 'https://www.gsmarena.com';
  private static readonly DELAY_MS = 2000; // Respectful delay between requests

  static async searchDevice(query: string): Promise<string[]> {
    try {
      await this.delay(this.DELAY_MS);
      
      const searchUrl = `${this.BASE_URL}/results.php3?sQuickSearch=yes&sName=${encodeURIComponent(query)}`;
      const response = await axios.get(searchUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });

      const $ = cheerio.load(response.data);
      const deviceLinks: string[] = [];

      $('.makers ul li a').each((_, element) => {
        const href = $(element).attr('href');
        if (href) {
          deviceLinks.push(`${this.BASE_URL}/${href}`);
        }
      });

      return deviceLinks.slice(0, 5); // Return top 5 results
    } catch (error) {
      console.error('Error searching GSMArena:', error);
      return [];
    }
  }

  static async scrapeDevice(url: string): Promise<GSMArenaDevice | null> {
    try {
      await this.delay(this.DELAY_MS);
      
      const response = await axios.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });

      const $ = cheerio.load(response.data);
      
      // Extract device name and brand
      const fullName = $('.specs-phone-name-title').text().trim();
      const [brand, ...nameParts] = fullName.split(' ');
      const name = nameParts.join(' ');

      const device: GSMArenaDevice = {
        name,
        brand
      };

      // Extract specifications from the specs table
      $('.specs-brief-accent').each((_, element) => {
        const label = $(element).find('.specs-brief-accent-title').text().toLowerCase();
        const value = $(element).find('.specs-brief-accent-value').text();

        if (label.includes('bluetooth')) {
          device.bluetooth = this.extractBluetoothVersion(value);
        }
      });

      // Extract detailed specs from main specs table
      $('table tr').each((_, row) => {
        const cells = $(row).find('td');
        if (cells.length >= 2) {
          const label = $(cells[0]).text().toLowerCase().trim();
          const value = $(cells[1]).text().trim();

          if (label.includes('bluetooth')) {
            device.bluetooth = this.extractBluetoothVersion(value);
          } else if (label.includes('usb')) {
            device.usbType = this.extractUSBType(value);
          } else if (label.includes('charging') || label.includes('fast charging')) {
            device.chargingProtocols = this.extractChargingProtocols(value);
          } else if (label.includes('loudspeaker') || label.includes('audio')) {
            device.supportedCodecs = this.extractAudioCodecs(value);
          } else if (label.includes('size')) {
            device.displaySize = value;
          } else if (label.includes('resolution')) {
            device.resolution = value;
          }
        }
      });

      return device;
    } catch (error) {
      console.error('Error scraping device from GSMArena:', error);
      return null;
    }
  }

  private static extractBluetoothVersion(text: string): string | undefined {
    const match = text.match(/(\d+\.\d+)/);
    return match ? match[1] : undefined;
  }

  private static extractUSBType(text: string): string | undefined {
    if (text.toLowerCase().includes('type-c') || text.toLowerCase().includes('usb-c')) {
      return 'Type-C';
    } else if (text.toLowerCase().includes('micro')) {
      return 'Micro-USB';
    } else if (text.toLowerCase().includes('lightning')) {
      return 'Lightning';
    }
    return undefined;
  }

  private static extractChargingProtocols(text: string): string[] {
    const protocols: string[] = [];
    const lowerText = text.toLowerCase();
    
    if (lowerText.includes('pd') || lowerText.includes('power delivery')) {
      protocols.push('PD');
    }
    if (lowerText.includes('quick charge') || lowerText.includes('qc')) {
      protocols.push('Quick Charge');
    }
    if (lowerText.includes('fast charging')) {
      protocols.push('Fast Charging');
    }
    if (lowerText.includes('wireless')) {
      protocols.push('Wireless');
    }
    
    return protocols;
  }

  private static extractAudioCodecs(text: string): string[] {
    const codecs: string[] = [];
    const lowerText = text.toLowerCase();
    
    if (lowerText.includes('ldac')) codecs.push('LDAC');
    if (lowerText.includes('aptx')) codecs.push('aptX');
    if (lowerText.includes('aac')) codecs.push('AAC');
    if (lowerText.includes('sbc')) codecs.push('SBC');
    if (lowerText.includes('hi-res') || lowerText.includes('24-bit')) {
      codecs.push('Hi-Res Audio');
    }
    
    return codecs.length > 0 ? codecs : ['AAC', 'SBC']; // Default codecs
  }

  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
