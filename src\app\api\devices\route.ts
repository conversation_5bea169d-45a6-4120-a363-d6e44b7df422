import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/database/connection';
import Device from '@/lib/database/models/Device';

export async function GET(request: NextRequest) {
  try {
    // Try to connect to database, but provide fallback data if it fails
    let devices = [];

    try {
      await connectDB();

      const { searchParams } = new URL(request.url);
      const type = searchParams.get('type');
      const search = searchParams.get('search');

      let query: any = {};

      if (type) {
        query.type = type;
      }

      if (search) {
        query.$or = [
          { name: { $regex: search, $options: 'i' } },
          { brand: { $regex: search, $options: 'i' } }
        ];
      }

      devices = await Device.find(query)
        .select('name brand type specs.bluetooth specs.supportedCodecs imageUrl')
        .sort({ name: 1 })
        .limit(50);
    } catch (dbError) {
      console.log('Database not available, using fallback data');
      // Fallback sample data when database is not available
      devices = getSampleDevices(request.nextUrl.searchParams.get('type'));
    }

    return NextResponse.json({ devices });
  } catch (error) {
    console.error('Error fetching devices:', error);
    return NextResponse.json(
      { devices: getSampleDevices(null) }
    );
  }
}

function getSampleDevices(type: string | null) {
  const sampleDevices = [
    {
      _id: '1',
      name: 'iPhone 15 Pro',
      brand: 'Apple',
      type: 'smartphone',
      specs: {
        bluetooth: '5.3',
        supportedCodecs: ['AAC', 'SBC']
      }
    },
    {
      _id: '2',
      name: 'Galaxy S24 Ultra',
      brand: 'Samsung',
      type: 'smartphone',
      specs: {
        bluetooth: '5.3',
        supportedCodecs: ['AAC', 'SBC', 'LDAC', 'Samsung Scalable']
      }
    },
    {
      _id: '3',
      name: 'MacBook Pro M3',
      brand: 'Apple',
      type: 'laptop',
      specs: {
        bluetooth: '5.3',
        supportedCodecs: ['AAC', 'SBC']
      }
    },
    {
      _id: '4',
      name: 'ThinkPad X1 Carbon',
      brand: 'Lenovo',
      type: 'laptop',
      specs: {
        bluetooth: '5.1',
        supportedCodecs: ['AAC', 'SBC']
      }
    },
    {
      _id: '5',
      name: 'iPad Pro',
      brand: 'Apple',
      type: 'tablet',
      specs: {
        bluetooth: '5.3',
        supportedCodecs: ['AAC', 'SBC']
      }
    }
  ];

  if (type) {
    return sampleDevices.filter(device => device.type === type);
  }

  return sampleDevices;
}

export async function POST(request: NextRequest) {
  try {
    await connectDB();
    
    const body = await request.json();
    const device = new Device(body);
    await device.save();
    
    return NextResponse.json({ device }, { status: 201 });
  } catch (error) {
    console.error('Error creating device:', error);
    return NextResponse.json(
      { error: 'Failed to create device' },
      { status: 500 }
    );
  }
}
