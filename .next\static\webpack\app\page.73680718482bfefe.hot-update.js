"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_DeviceSelector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/DeviceSelector */ \"(app-pages-browser)/./src/components/DeviceSelector.tsx\");\n/* harmony import */ var _components_AccessorySelector__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AccessorySelector */ \"(app-pages-browser)/./src/components/AccessorySelector.tsx\");\n/* harmony import */ var _components_CompatibilityResult__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/CompatibilityResult */ \"(app-pages-browser)/./src/components/CompatibilityResult.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Badge */ \"(app-pages-browser)/./src/components/ui/Badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/headphones.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [selectedDevice, setSelectedDevice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedAccessory, setSelectedAccessory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [compatibilityResult, setCompatibilityResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showChecker, setShowChecker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const checkCompatibility = async ()=>{\n        if (!selectedDevice || !selectedAccessory) return;\n        setLoading(true);\n        try {\n            const response = await fetch('/api/check-compatibility', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    deviceId: selectedDevice._id,\n                    accessoryId: selectedAccessory._id\n                })\n            });\n            const data = await response.json();\n            setCompatibilityResult(data);\n        } catch (error) {\n            console.error('Error checking compatibility:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const resetSelection = ()=>{\n        setSelectedDevice(null);\n        setSelectedAccessory(null);\n        setCompatibilityResult(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\",\n        children: !showChecker ? // Landing Page\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"relative overflow-hidden bg-gradient-to-br from-indigo-900 via-blue-900 to-purple-900\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 opacity-20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0\",\n                                style: {\n                                    backgroundImage: \"radial-gradient(circle at 25px 25px, rgba(255,255,255,0.1) 2px, transparent 0)\",\n                                    backgroundSize: '50px 50px'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white/90 text-sm font-medium mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Save Money • Avoid Incompatible Purchases\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-5xl md:text-7xl font-extrabold mb-8 bg-gradient-to-r from-white via-blue-100 to-purple-200 bg-clip-text text-transparent leading-tight\",\n                                        children: [\n                                            \"Don't Waste Money on Features\",\n                                            ' ',\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent\",\n                                                children: \"You Can't Use\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl md:text-2xl mb-12 text-blue-100 max-w-4xl mx-auto leading-relaxed\",\n                                        children: \"Get instant compatibility analysis for your tech purchases. Know exactly what features will work with your devices before you buy.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-4 justify-center items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                size: \"lg\",\n                                                onClick: ()=>setShowChecker(true),\n                                                className: \"bg-gradient-to-r from-yellow-400 to-orange-500 text-black hover:from-yellow-300 hover:to-orange-400 text-lg px-8 py-4 font-semibold shadow-2xl hover:shadow-yellow-500/25 transition-all duration-300 transform hover:scale-105\",\n                                                children: [\n                                                    \"Check Compatibility Now\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"ml-2\",\n                                                        size: 20\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 112,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                variant: \"outline\",\n                                                size: \"lg\",\n                                                className: \"border-white/30 text-white hover:bg-white/10 backdrop-blur-sm px-8 py-4\",\n                                                onClick: ()=>{\n                                                    var _document_getElementById;\n                                                    (_document_getElementById = document.getElementById('how-it-works')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                                                        behavior: 'smooth'\n                                                    });\n                                                },\n                                                children: \"Learn How It Works\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-8 mt-16 max-w-2xl mx-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-white mb-2\",\n                                                        children: \"1000+\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-blue-200 text-sm\",\n                                                        children: \"Devices Supported\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-white mb-2\",\n                                                        children: \"95%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-blue-200 text-sm\",\n                                                        children: \"Accuracy Rate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-white mb-2\",\n                                                        children: \"$500+\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-blue-200 text-sm\",\n                                                        children: \"Average Savings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    animate: {\n                                        y: [\n                                            0,\n                                            -30,\n                                            0\n                                        ],\n                                        rotate: [\n                                            0,\n                                            10,\n                                            0\n                                        ],\n                                        scale: [\n                                            1,\n                                            1.1,\n                                            1\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 6,\n                                        repeat: Infinity,\n                                        ease: \"easeInOut\"\n                                    },\n                                    className: \"absolute top-20 left-10 lg:left-20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            size: 32,\n                                            className: \"text-blue-300\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    animate: {\n                                        y: [\n                                            0,\n                                            25,\n                                            0\n                                        ],\n                                        rotate: [\n                                            0,\n                                            -8,\n                                            0\n                                        ],\n                                        scale: [\n                                            1,\n                                            1.05,\n                                            1\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 5,\n                                        repeat: Infinity,\n                                        ease: \"easeInOut\",\n                                        delay: 1\n                                    },\n                                    className: \"absolute top-32 right-10 lg:right-20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            size: 28,\n                                            className: \"text-purple-300\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    animate: {\n                                        y: [\n                                            0,\n                                            -20,\n                                            0\n                                        ],\n                                        rotate: [\n                                            0,\n                                            5,\n                                            0\n                                        ],\n                                        scale: [\n                                            1,\n                                            1.08,\n                                            1\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 7,\n                                        repeat: Infinity,\n                                        ease: \"easeInOut\",\n                                        delay: 2\n                                    },\n                                    className: \"absolute bottom-32 left-16 lg:left-32\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            size: 30,\n                                            className: \"text-green-300\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    animate: {\n                                        y: [\n                                            0,\n                                            18,\n                                            0\n                                        ],\n                                        rotate: [\n                                            0,\n                                            -6,\n                                            0\n                                        ],\n                                        scale: [\n                                            1,\n                                            1.06,\n                                            1\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 4.5,\n                                        repeat: Infinity,\n                                        ease: \"easeInOut\",\n                                        delay: 0.5\n                                    },\n                                    className: \"absolute bottom-20 right-16 lg:right-32\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            size: 26,\n                                            className: \"text-yellow-300\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    id: \"how-it-works\",\n                    className: \"py-24 bg-white relative overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 left-0 w-full h-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-20 left-10 w-72 h-72 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-40 right-10 w-72 h-72 bg-purple-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse\",\n                                    style: {\n                                        animationDelay: '2s'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-20 left-1/2 w-72 h-72 bg-indigo-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse\",\n                                    style: {\n                                        animationDelay: '4s'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"text-center mb-20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 text-sm font-medium mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"w-2 h-2 bg-blue-500 rounded-full mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Simple Process\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent mb-6\",\n                                            children: \"How It Works\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\",\n                                            children: \"Get detailed compatibility analysis in just 3 simple steps. Our AI-powered system analyzes thousands of device specifications instantly.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-3 gap-8 lg:gap-12\",\n                                    children: [\n                                        {\n                                            step: \"01\",\n                                            title: \"Select Your Device\",\n                                            description: \"Choose your smartphone, laptop, or tablet from our comprehensive database of 1000+ devices\",\n                                            icon: _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                            gradient: \"from-blue-500 to-cyan-500\",\n                                            bgGradient: \"from-blue-50 to-cyan-50\"\n                                        },\n                                        {\n                                            step: \"02\",\n                                            title: \"Pick an Accessory\",\n                                            description: \"Select the accessory you want to check - TWS earbuds, SSD, charger, cable, monitor, or soundbar\",\n                                            icon: _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                            gradient: \"from-purple-500 to-pink-500\",\n                                            bgGradient: \"from-purple-50 to-pink-50\"\n                                        },\n                                        {\n                                            step: \"03\",\n                                            title: \"Get Smart Results\",\n                                            description: \"Receive detailed compatibility analysis with AI explanations, recommendations, and direct purchase links\",\n                                            icon: _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                            gradient: \"from-green-500 to-emerald-500\",\n                                            bgGradient: \"from-green-50 to-emerald-50\"\n                                        }\n                                    ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8,\n                                                delay: index * 0.2\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            whileHover: {\n                                                y: -8,\n                                                transition: {\n                                                    duration: 0.3\n                                                }\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative p-8 rounded-3xl bg-gradient-to-br \".concat(item.bgGradient, \" border border-white/50 shadow-xl hover:shadow-2xl transition-all duration-300 h-full group\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -top-4 -left-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 rounded-2xl bg-gradient-to-r \".concat(item.gradient, \" flex items-center justify-center text-white font-bold text-lg shadow-lg\"),\n                                                            children: item.step\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-20 h-20 rounded-2xl bg-gradient-to-r \".concat(item.gradient, \" flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                            size: 36,\n                                                            className: \"text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                                children: item.title\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 leading-relaxed\",\n                                                                children: item.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-4 right-4 w-8 h-8 bg-white/20 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute bottom-4 left-4 w-6 h-6 bg-white/10 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"py-24 bg-gradient-to-br from-gray-50 to-blue-50 relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 opacity-30\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0\",\n                                style: {\n                                    backgroundImage: \"radial-gradient(circle at 20px 20px, rgba(0,0,0,0.02) 10px, transparent 0)\",\n                                    backgroundSize: '40px 40px'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"text-center mb-20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-indigo-100 to-blue-100 text-indigo-800 text-sm font-medium mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"w-2 h-2 bg-indigo-500 rounded-full mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Real Examples\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent mb-6\",\n                                            children: \"See It In Action\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\",\n                                            children: \"Real compatibility scenarios showing how our tool prevents costly mistakes and helps you make informed decisions.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid lg:grid-cols-2 gap-8 lg:gap-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: -30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            whileHover: {\n                                                y: -5,\n                                                transition: {\n                                                    duration: 0.3\n                                                }\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative p-8 bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-300 border border-gray-100 overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full -translate-y-16 translate-x-16 opacity-50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative flex items-center justify-center space-x-6 mb-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"text-white\",\n                                                                            size: 28\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 363,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 362,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -bottom-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"text-white\",\n                                                                            size: 14\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 366,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 365,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-3xl font-bold text-gray-300\",\n                                                                children: \"+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"text-white\",\n                                                                            size: 28\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 374,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 373,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -bottom-2 -right-2 w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"text-white\",\n                                                                            size: 14\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 377,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 376,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-bold text-gray-900 mb-2\",\n                                                                children: \"iPhone 15 Pro + Sony WF-1000XM4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"inline-flex items-center px-3 py-1 rounded-full bg-yellow-100 text-yellow-800 text-sm font-medium\",\n                                                                children: \"75% Compatible\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between p-3 bg-green-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"text-green-600 mr-2\",\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 392,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-green-800\",\n                                                                                children: \"AAC Codec\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 393,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 391,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                        variant: \"success\",\n                                                                        className: \"text-xs\",\n                                                                        children: \"Works\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 395,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between p-3 bg-red-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"text-red-600 mr-2\",\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 400,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-red-800\",\n                                                                                children: \"LDAC Support\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 401,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 399,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                        variant: \"error\",\n                                                                        className: \"text-xs\",\n                                                                        children: \"Missing\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 403,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 398,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between p-3 bg-yellow-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"text-yellow-600 mr-2\",\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 408,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-yellow-800\",\n                                                                                children: \"Hi-Res Audio\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 409,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 407,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                        variant: \"warning\",\n                                                                        className: \"text-xs\",\n                                                                        children: \"Limited\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 411,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 406,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 bg-gray-50 rounded-xl\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 leading-relaxed\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    className: \"text-gray-900\",\n                                                                    children: \"Result:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 417,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" Great sound quality with AAC, but you won't get LDAC's hi-res audio benefits. Consider alternatives for audiophile experience.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 416,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8,\n                                                delay: 0.2\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            whileHover: {\n                                                y: -5,\n                                                transition: {\n                                                    duration: 0.3\n                                                }\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative p-8 bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-300 border border-gray-100 overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-0 left-0 w-32 h-32 bg-gradient-to-br from-green-100 to-emerald-100 rounded-full -translate-y-16 -translate-x-16 opacity-50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative flex items-center justify-center space-x-6 mb-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-16 h-16 bg-gradient-to-r from-gray-600 to-gray-700 rounded-2xl flex items-center justify-center shadow-lg\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-8 h-8 text-white\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M20 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4V8h16v10z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 439,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 438,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 437,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -bottom-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"text-white\",\n                                                                            size: 14\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 443,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 442,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 436,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-3xl font-bold text-gray-300\",\n                                                                children: \"+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 447,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"text-white\",\n                                                                            size: 28\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 451,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 450,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -bottom-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"text-white\",\n                                                                            size: 14\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 454,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 453,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-bold text-gray-900 mb-2\",\n                                                                children: \"MacBook Pro M3 + Samsung 980 PRO\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"inline-flex items-center px-3 py-1 rounded-full bg-green-100 text-green-800 text-sm font-medium\",\n                                                                children: \"100% Compatible\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between p-3 bg-green-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"text-green-600 mr-2\",\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 469,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-green-800\",\n                                                                                children: \"NVMe Interface\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 470,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 468,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                        variant: \"success\",\n                                                                        className: \"text-xs\",\n                                                                        children: \"Perfect\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 472,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between p-3 bg-green-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"text-green-600 mr-2\",\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 477,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-green-800\",\n                                                                                children: \"PCIe 4.0 Support\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 478,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 476,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                        variant: \"success\",\n                                                                        className: \"text-xs\",\n                                                                        children: \"Supported\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 480,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between p-3 bg-green-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"text-green-600 mr-2\",\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 485,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-green-800\",\n                                                                                children: \"Full Speed (7GB/s)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 486,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 484,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                        variant: \"success\",\n                                                                        className: \"text-xs\",\n                                                                        children: \"Maximum\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 488,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 483,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 bg-green-50 rounded-xl\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 leading-relaxed\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    className: \"text-gray-900\",\n                                                                    children: \"Result:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 494,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" Perfect match! You'll get the full 7GB/s read speeds for lightning-fast performance. Ideal for video editing and large file transfers.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.4\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"text-center mt-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative inline-block\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur-lg opacity-30 animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    size: \"lg\",\n                                                    onClick: ()=>setShowChecker(true),\n                                                    className: \"relative bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg font-semibold shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105\",\n                                                    children: [\n                                                        \"Try It Yourself - It's Free\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"ml-2\",\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 516,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 mt-4\",\n                                            children: \"No signup required • Instant results\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 318,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 75,\n            columnNumber: 9\n        }, this) : // Compatibility Checker\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white/80 backdrop-blur-md shadow-lg border-b border-gray-200/50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-4 lg:mb-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"text-white\",\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-3xl lg:text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent\",\n                                                        children: \"Compatibility Checker\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg text-gray-600\",\n                                                        children: \"Get instant compatibility analysis for your tech purchases\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 530,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.2\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>{\n                                            setShowChecker(false);\n                                            setCompatibilityResult(null);\n                                            setSelectedDevice(null);\n                                            setSelectedAccessory(null);\n                                        },\n                                        className: \"border-gray-300 hover:bg-gray-50 px-6 py-3\",\n                                        children: \"← Back to Home\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 529,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 528,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 527,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: !compatibilityResult ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DeviceSelector__WEBPACK_IMPORTED_MODULE_2__.DeviceSelector, {\n                                    onDeviceSelect: setSelectedDevice,\n                                    selectedDevice: selectedDevice\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 577,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 576,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AccessorySelector__WEBPACK_IMPORTED_MODULE_3__.AccessorySelector, {\n                                    onAccessorySelect: setSelectedAccessory,\n                                    selectedAccessory: selectedAccessory\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 585,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 584,\n                                columnNumber: 17\n                            }, this),\n                            selectedDevice && selectedAccessory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: checkCompatibility,\n                                    loading: loading,\n                                    size: \"lg\",\n                                    className: \"px-8 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"mr-2\",\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 600,\n                                            columnNumber: 23\n                                        }, this),\n                                        \"Check Compatibility\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 594,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 593,\n                                columnNumber: 19\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 574,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: resetSelection,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    children: \"← Check Another Combination\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 609,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 608,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CompatibilityResult__WEBPACK_IMPORTED_MODULE_4__.CompatibilityResult, {\n                                data: compatibilityResult\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 607,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 572,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 526,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"lRPJOyAYcgr3wdpYHy6KWuM0LnE=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});