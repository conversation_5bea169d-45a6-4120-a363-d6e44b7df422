'use client';

import React, { useState } from 'react';
import { DeviceSelector } from '@/components/DeviceSelector';
import { AccessorySelector } from '@/components/AccessorySelector';
import { CompatibilityResult } from '@/components/CompatibilityResult';
import { Button } from '@/components/ui/Button';
import { Search } from 'lucide-react';

interface Device {
  _id: string;
  name: string;
  brand: string;
  type: string;
  specs: any;
}

interface Accessory {
  _id: string;
  name: string;
  brand: string;
  type: string;
  specs: any;
  price?: {
    amount: number;
    currency: string;
  };
}

export default function Home() {
  const [selectedDevice, setSelectedDevice] = useState<Device | null>(null);
  const [selectedAccessory, setSelectedAccessory] = useState<Accessory | null>(null);
  const [compatibilityResult, setCompatibilityResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const checkCompatibility = async () => {
    if (!selectedDevice || !selectedAccessory) return;

    setLoading(true);
    try {
      const response = await fetch('/api/check-compatibility', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          deviceId: selectedDevice._id,
          accessoryId: selectedAccessory._id,
        }),
      });

      const data = await response.json();
      setCompatibilityResult(data);
    } catch (error) {
      console.error('Error checking compatibility:', error);
    } finally {
      setLoading(false);
    }
  };

  const resetSelection = () => {
    setSelectedDevice(null);
    setSelectedAccessory(null);
    setCompatibilityResult(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900">
              Product Compatibility Checker
            </h1>
            <p className="mt-2 text-lg text-gray-600">
              Find out if your tech products work perfectly together
            </p>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {!compatibilityResult ? (
          <div className="grid lg:grid-cols-2 gap-8">
            {/* Device Selection */}
            <div className="bg-white rounded-lg shadow p-6">
              <DeviceSelector
                onDeviceSelect={setSelectedDevice}
                selectedDevice={selectedDevice}
              />
            </div>

            {/* Accessory Selection */}
            <div className="bg-white rounded-lg shadow p-6">
              <AccessorySelector
                onAccessorySelect={setSelectedAccessory}
                selectedAccessory={selectedAccessory}
              />
            </div>

            {/* Check Compatibility Button */}
            {selectedDevice && selectedAccessory && (
              <div className="lg:col-span-2 text-center">
                <Button
                  onClick={checkCompatibility}
                  loading={loading}
                  size="lg"
                  className="px-8"
                >
                  <Search className="mr-2" size={20} />
                  Check Compatibility
                </Button>
              </div>
            )}
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow p-6">
            <div className="mb-6 text-center">
              <Button
                onClick={resetSelection}
                variant="outline"
                size="sm"
              >
                ← Check Another Combination
              </Button>
            </div>
            <CompatibilityResult data={compatibilityResult} />
          </div>
        )}

        {/* Info Section */}
        <div className="mt-12 bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            How It Works
          </h2>
          <div className="grid md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <span className="text-blue-600 font-bold text-lg">1</span>
              </div>
              <h3 className="font-medium text-gray-900 mb-2">Select Your Device</h3>
              <p className="text-sm text-gray-600">
                Choose your smartphone, laptop, or tablet from our database
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <span className="text-blue-600 font-bold text-lg">2</span>
              </div>
              <h3 className="font-medium text-gray-900 mb-2">Pick an Accessory</h3>
              <p className="text-sm text-gray-600">
                Select the accessory you want to check compatibility for
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <span className="text-blue-600 font-bold text-lg">3</span>
              </div>
              <h3 className="font-medium text-gray-900 mb-2">Get Results</h3>
              <p className="text-sm text-gray-600">
                See detailed compatibility analysis and recommendations
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
