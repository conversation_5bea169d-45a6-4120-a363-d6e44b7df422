'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { DeviceSelector } from '@/components/DeviceSelector';
import { AccessorySelector } from '@/components/AccessorySelector';
import { CompatibilityResult } from '@/components/CompatibilityResult';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Search, Smartphone, Headphones, HardDrive, Zap, CheckCircle, XCircle, AlertTriangle, ArrowRight } from 'lucide-react';

interface Device {
  _id: string;
  name: string;
  brand: string;
  type: string;
  specs: any;
}

interface Accessory {
  _id: string;
  name: string;
  brand: string;
  type: string;
  specs: any;
  price?: {
    amount: number;
    currency: string;
  };
}

export default function Home() {
  const [selectedDevice, setSelectedDevice] = useState<Device | null>(null);
  const [selectedAccessory, setSelectedAccessory] = useState<Accessory | null>(null);
  const [compatibilityResult, setCompatibilityResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [showChecker, setShowChecker] = useState(false);

  const checkCompatibility = async () => {
    if (!selectedDevice || !selectedAccessory) return;

    setLoading(true);
    try {
      const response = await fetch('/api/check-compatibility', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          deviceId: selectedDevice._id,
          accessoryId: selectedAccessory._id,
        }),
      });

      const data = await response.json();
      setCompatibilityResult(data);
    } catch (error) {
      console.error('Error checking compatibility:', error);
    } finally {
      setLoading(false);
    }
  };

  const resetSelection = () => {
    setSelectedDevice(null);
    setSelectedAccessory(null);
    setCompatibilityResult(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {!showChecker ? (
        // Landing Page
        <div className="relative">
          {/* Hero Section */}
          <section className="relative overflow-hidden bg-gradient-to-r from-blue-600 to-purple-600 text-white">
            <div className="absolute inset-0 bg-black/20"></div>
            <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                className="text-center"
              >
                <h1 className="text-4xl md:text-6xl font-bold mb-6">
                  Don't Waste Money on Features{' '}
                  <span className="text-yellow-300">You Can't Use</span>
                </h1>
                <p className="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto">
                  Check if your tech accessories are fully compatible with your devices before you buy
                </p>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                >
                  <Button
                    size="lg"
                    onClick={() => setShowChecker(true)}
                    className="bg-yellow-400 text-black hover:bg-yellow-300 text-lg px-8 py-4"
                  >
                    Check Compatibility Now
                    <ArrowRight className="ml-2" size={20} />
                  </Button>
                </motion.div>
              </motion.div>
            </div>

            {/* Floating Icons Animation */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
              <motion.div
                animate={{
                  y: [0, -20, 0],
                  rotate: [0, 5, 0]
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="absolute top-20 left-10 text-white/20"
              >
                <Smartphone size={40} />
              </motion.div>
              <motion.div
                animate={{
                  y: [0, 20, 0],
                  rotate: [0, -5, 0]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 1
                }}
                className="absolute top-32 right-20 text-white/20"
              >
                <Headphones size={35} />
              </motion.div>
              <motion.div
                animate={{
                  y: [0, -15, 0],
                  rotate: [0, 3, 0]
                }}
                transition={{
                  duration: 5,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 2
                }}
                className="absolute bottom-20 left-20 text-white/20"
              >
                <HardDrive size={30} />
              </motion.div>
            </div>
          </section>

          {/* How It Works Section */}
          <section className="py-20 bg-white">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="text-center mb-16"
              >
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                  How It Works
                </h2>
                <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                  Get detailed compatibility analysis in just 3 simple steps
                </p>
              </motion.div>

              <div className="grid md:grid-cols-3 gap-8">
                {[
                  {
                    step: "1",
                    title: "Select Your Device",
                    description: "Choose your smartphone, laptop, or tablet from our comprehensive database",
                    icon: Smartphone,
                    color: "bg-blue-100 text-blue-600"
                  },
                  {
                    step: "2",
                    title: "Pick an Accessory",
                    description: "Select the accessory you want to check - TWS earbuds, SSD, charger, or more",
                    icon: Headphones,
                    color: "bg-purple-100 text-purple-600"
                  },
                  {
                    step: "3",
                    title: "Get Smart Results",
                    description: "See detailed compatibility analysis with recommendations and buy links",
                    icon: CheckCircle,
                    color: "bg-green-100 text-green-600"
                  }
                ].map((item, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.2 }}
                    viewport={{ once: true }}
                  >
                    <Card className="text-center h-full hover:shadow-lg transition-shadow">
                      <CardHeader>
                        <div className={`w-16 h-16 rounded-full ${item.color} flex items-center justify-center mx-auto mb-4`}>
                          <item.icon size={32} />
                        </div>
                        <div className="w-8 h-8 bg-gray-900 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-sm font-bold">
                          {item.step}
                        </div>
                        <CardTitle className="text-xl">{item.title}</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <CardDescription className="text-base">
                          {item.description}
                        </CardDescription>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </div>
          </section>

          {/* Example Cases Section */}
          <section className="py-20 bg-gray-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="text-center mb-16"
              >
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                  Real Compatibility Examples
                </h2>
                <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                  See how our tool helps you make informed decisions
                </p>
              </motion.div>

              <div className="grid md:grid-cols-2 gap-8">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                >
                  <Card className="h-full">
                    <CardHeader>
                      <div className="flex items-center space-x-4 mb-4">
                        <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                          <Smartphone className="text-blue-600" size={24} />
                        </div>
                        <div className="text-2xl">+</div>
                        <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                          <Headphones className="text-purple-600" size={24} />
                        </div>
                      </div>
                      <CardTitle>iPhone 15 Pro + Sony WF-1000XM4</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center space-x-2">
                          <Badge variant="success">
                            <CheckCircle size={12} className="mr-1" />
                            AAC Codec
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge variant="error">
                            <XCircle size={12} className="mr-1" />
                            LDAC Not Supported
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge variant="warning">
                            <AlertTriangle size={12} className="mr-1" />
                            Limited Hi-Res Audio
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600 mt-4">
                          <strong>Result:</strong> 75% compatible. You'll get great sound quality with AAC, but won't benefit from LDAC's hi-res audio capabilities.
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  viewport={{ once: true }}
                >
                  <Card className="h-full">
                    <CardHeader>
                      <div className="flex items-center space-x-4 mb-4">
                        <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                          <svg className="w-6 h-6 text-gray-600" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M20 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4V8h16v10z"/>
                          </svg>
                        </div>
                        <div className="text-2xl">+</div>
                        <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                          <HardDrive className="text-green-600" size={24} />
                        </div>
                      </div>
                      <CardTitle>MacBook Pro M3 + Samsung 980 PRO</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center space-x-2">
                          <Badge variant="success">
                            <CheckCircle size={12} className="mr-1" />
                            NVMe Interface
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge variant="success">
                            <CheckCircle size={12} className="mr-1" />
                            PCIe 4.0 Support
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge variant="success">
                            <CheckCircle size={12} className="mr-1" />
                            Full Speed (7GB/s)
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600 mt-4">
                          <strong>Result:</strong> 100% compatible. Perfect match! You'll get the full 7GB/s read speeds for lightning-fast performance.
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              </div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                viewport={{ once: true }}
                className="text-center mt-12"
              >
                <Button
                  size="lg"
                  onClick={() => setShowChecker(true)}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Try It Yourself
                  <ArrowRight className="ml-2" size={20} />
                </Button>
              </motion.div>
            </div>
          </section>
        </div>
      ) : (
        // Compatibility Checker
        <div className="min-h-screen bg-gray-50">
          <div className="bg-white shadow-sm border-b">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">
                    Compatibility Checker
                  </h1>
                  <p className="mt-2 text-lg text-gray-600">
                    Check if your devices and accessories work perfectly together
                  </p>
                </div>
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowChecker(false);
                    setCompatibilityResult(null);
                    setSelectedDevice(null);
                    setSelectedAccessory(null);
                  }}
                >
                  ← Back to Home
                </Button>
              </div>
            </div>
          </div>

          <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {!compatibilityResult ? (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="grid lg:grid-cols-2 gap-8"
              >
                {/* Device Selection */}
                <Card>
                  <CardContent className="p-6">
                    <DeviceSelector
                      onDeviceSelect={setSelectedDevice}
                      selectedDevice={selectedDevice}
                    />
                  </CardContent>
                </Card>

                {/* Accessory Selection */}
                <Card>
                  <CardContent className="p-6">
                    <AccessorySelector
                      onAccessorySelect={setSelectedAccessory}
                      selectedAccessory={selectedAccessory}
                    />
                  </CardContent>
                </Card>

                {/* Check Compatibility Button */}
                {selectedDevice && selectedAccessory && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3 }}
                    className="lg:col-span-2 text-center"
                  >
                    <Button
                      onClick={checkCompatibility}
                      loading={loading}
                      size="lg"
                      className="px-8 bg-blue-600 hover:bg-blue-700"
                    >
                      <Search className="mr-2" size={20} />
                      Check Compatibility
                    </Button>
                  </motion.div>
                )}
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <Card>
                  <CardContent className="p-6">
                    <div className="mb-6 text-center">
                      <Button
                        onClick={resetSelection}
                        variant="outline"
                        size="sm"
                      >
                        ← Check Another Combination
                      </Button>
                    </div>
                    <CompatibilityResult data={compatibilityResult} />
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </main>
        </div>
      )}

    </div>
  );
}
