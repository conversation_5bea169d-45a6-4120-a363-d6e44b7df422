'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { DeviceSelector } from '@/components/DeviceSelector';
import { AccessorySelector } from '@/components/AccessorySelector';
import { CompatibilityResult } from '@/components/CompatibilityResult';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Search, Smartphone, Headphones, HardDrive, Zap, CheckCircle, XCircle, AlertTriangle, ArrowRight } from 'lucide-react';

interface Device {
  _id: string;
  name: string;
  brand: string;
  type: string;
  specs: any;
}

interface Accessory {
  _id: string;
  name: string;
  brand: string;
  type: string;
  specs: any;
  price?: {
    amount: number;
    currency: string;
  };
}

export default function Home() {
  const [selectedDevice, setSelectedDevice] = useState<Device | null>(null);
  const [selectedAccessory, setSelectedAccessory] = useState<Accessory | null>(null);
  const [compatibilityResult, setCompatibilityResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [showChecker, setShowChecker] = useState(false);

  const checkCompatibility = async () => {
    if (!selectedDevice || !selectedAccessory) return;

    setLoading(true);
    try {
      const response = await fetch('/api/check-compatibility', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          deviceId: selectedDevice._id,
          accessoryId: selectedAccessory._id,
        }),
      });

      const data = await response.json();
      setCompatibilityResult(data);
    } catch (error) {
      console.error('Error checking compatibility:', error);
    } finally {
      setLoading(false);
    }
  };

  const resetSelection = () => {
    setSelectedDevice(null);
    setSelectedAccessory(null);
    setCompatibilityResult(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {!showChecker ? (
        // Landing Page
        <div className="relative">
          {/* Hero Section */}
          <section className="relative overflow-hidden bg-gradient-to-br from-indigo-900 via-blue-900 to-purple-900">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-20">
              <div className="absolute inset-0" style={{
                backgroundImage: `radial-gradient(circle at 25px 25px, rgba(255,255,255,0.1) 2px, transparent 0)`,
                backgroundSize: '50px 50px'
              }}></div>
            </div>

            <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 1, ease: "easeOut" }}
                className="text-center"
              >
                {/* Badge */}
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="inline-flex items-center px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white/90 text-sm font-medium mb-8"
                >
                  <span className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
                  Save Money • Avoid Incompatible Purchases
                </motion.div>

                <h1 className="text-5xl md:text-7xl font-extrabold mb-8 bg-gradient-to-r from-white via-blue-100 to-purple-200 bg-clip-text text-transparent leading-tight">
                  Don't Waste Money on Features{' '}
                  <span className="bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">
                    You Can't Use
                  </span>
                </h1>

                <p className="text-xl md:text-2xl mb-12 text-blue-100 max-w-4xl mx-auto leading-relaxed">
                  Get instant compatibility analysis for your tech purchases. Know exactly what features will work with your devices before you buy.
                </p>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                  className="flex flex-col sm:flex-row gap-4 justify-center items-center"
                >
                  <Button
                    size="lg"
                    onClick={() => setShowChecker(true)}
                    className="bg-gradient-to-r from-yellow-400 to-orange-500 text-black hover:from-yellow-300 hover:to-orange-400 text-lg px-8 py-4 font-semibold shadow-2xl hover:shadow-yellow-500/25 transition-all duration-300 transform hover:scale-105"
                  >
                    Check Compatibility Now
                    <ArrowRight className="ml-2" size={20} />
                  </Button>

                  <Button
                    variant="outline"
                    size="lg"
                    className="border-white/30 text-white hover:bg-white/10 backdrop-blur-sm px-8 py-4"
                    onClick={() => {
                      document.getElementById('how-it-works')?.scrollIntoView({ behavior: 'smooth' });
                    }}
                  >
                    Learn How It Works
                  </Button>
                </motion.div>

                {/* Stats */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.6 }}
                  className="grid grid-cols-3 gap-8 mt-16 max-w-2xl mx-auto"
                >
                  <div className="text-center">
                    <div className="text-3xl font-bold text-white mb-2">1000+</div>
                    <div className="text-blue-200 text-sm">Devices Supported</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-white mb-2">95%</div>
                    <div className="text-blue-200 text-sm">Accuracy Rate</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-white mb-2">$500+</div>
                    <div className="text-blue-200 text-sm">Average Savings</div>
                  </div>
                </motion.div>
              </motion.div>
            </div>

            {/* Floating Icons Animation */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
              <motion.div
                animate={{
                  y: [0, -30, 0],
                  rotate: [0, 10, 0],
                  scale: [1, 1.1, 1]
                }}
                transition={{
                  duration: 6,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="absolute top-20 left-10 lg:left-20"
              >
                <div className="p-4 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20">
                  <Smartphone size={32} className="text-blue-300" />
                </div>
              </motion.div>

              <motion.div
                animate={{
                  y: [0, 25, 0],
                  rotate: [0, -8, 0],
                  scale: [1, 1.05, 1]
                }}
                transition={{
                  duration: 5,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 1
                }}
                className="absolute top-32 right-10 lg:right-20"
              >
                <div className="p-4 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20">
                  <Headphones size={28} className="text-purple-300" />
                </div>
              </motion.div>

              <motion.div
                animate={{
                  y: [0, -20, 0],
                  rotate: [0, 5, 0],
                  scale: [1, 1.08, 1]
                }}
                transition={{
                  duration: 7,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 2
                }}
                className="absolute bottom-32 left-16 lg:left-32"
              >
                <div className="p-4 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20">
                  <HardDrive size={30} className="text-green-300" />
                </div>
              </motion.div>

              <motion.div
                animate={{
                  y: [0, 18, 0],
                  rotate: [0, -6, 0],
                  scale: [1, 1.06, 1]
                }}
                transition={{
                  duration: 4.5,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 0.5
                }}
                className="absolute bottom-20 right-16 lg:right-32"
              >
                <div className="p-4 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20">
                  <Zap size={26} className="text-yellow-300" />
                </div>
              </motion.div>
            </div>
          </section>

          {/* How It Works Section */}
          <section id="how-it-works" className="py-24 bg-white relative overflow-hidden">
            {/* Background Elements */}
            <div className="absolute top-0 left-0 w-full h-full">
              <div className="absolute top-20 left-10 w-72 h-72 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
              <div className="absolute top-40 right-10 w-72 h-72 bg-purple-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse" style={{ animationDelay: '2s' }}></div>
              <div className="absolute bottom-20 left-1/2 w-72 h-72 bg-indigo-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse" style={{ animationDelay: '4s' }}></div>
            </div>

            <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="text-center mb-20"
              >
                <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 text-sm font-medium mb-6">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                  Simple Process
                </div>
                <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent mb-6">
                  How It Works
                </h2>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                  Get detailed compatibility analysis in just 3 simple steps. Our AI-powered system analyzes thousands of device specifications instantly.
                </p>
              </motion.div>

              <div className="grid md:grid-cols-3 gap-8 lg:gap-12">
                {[
                  {
                    step: "01",
                    title: "Select Your Device",
                    description: "Choose your smartphone, laptop, or tablet from our comprehensive database of 1000+ devices",
                    icon: Smartphone,
                    gradient: "from-blue-500 to-cyan-500",
                    bgGradient: "from-blue-50 to-cyan-50"
                  },
                  {
                    step: "02",
                    title: "Pick an Accessory",
                    description: "Select the accessory you want to check - TWS earbuds, SSD, charger, cable, monitor, or soundbar",
                    icon: Headphones,
                    gradient: "from-purple-500 to-pink-500",
                    bgGradient: "from-purple-50 to-pink-50"
                  },
                  {
                    step: "03",
                    title: "Get Smart Results",
                    description: "Receive detailed compatibility analysis with AI explanations, recommendations, and direct purchase links",
                    icon: CheckCircle,
                    gradient: "from-green-500 to-emerald-500",
                    bgGradient: "from-green-50 to-emerald-50"
                  }
                ].map((item, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: index * 0.2 }}
                    viewport={{ once: true }}
                    whileHover={{ y: -8, transition: { duration: 0.3 } }}
                  >
                    <div className={`relative p-8 rounded-3xl bg-gradient-to-br ${item.bgGradient} border border-white/50 shadow-xl hover:shadow-2xl transition-all duration-300 h-full group`}>
                      {/* Step Number */}
                      <div className="absolute -top-4 -left-4">
                        <div className={`w-12 h-12 rounded-2xl bg-gradient-to-r ${item.gradient} flex items-center justify-center text-white font-bold text-lg shadow-lg`}>
                          {item.step}
                        </div>
                      </div>

                      {/* Icon */}
                      <div className={`w-20 h-20 rounded-2xl bg-gradient-to-r ${item.gradient} flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300`}>
                        <item.icon size={36} className="text-white" />
                      </div>

                      {/* Content */}
                      <div className="text-center">
                        <h3 className="text-2xl font-bold text-gray-900 mb-4">{item.title}</h3>
                        <p className="text-gray-600 leading-relaxed">{item.description}</p>
                      </div>

                      {/* Decorative Elements */}
                      <div className="absolute top-4 right-4 w-8 h-8 bg-white/20 rounded-full"></div>
                      <div className="absolute bottom-4 left-4 w-6 h-6 bg-white/10 rounded-full"></div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </section>

          {/* Example Cases Section */}
          <section className="py-24 bg-gradient-to-br from-gray-50 to-blue-50 relative">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-30">
              <div className="absolute inset-0" style={{
                backgroundImage: `radial-gradient(circle at 20px 20px, rgba(0,0,0,0.02) 10px, transparent 0)`,
                backgroundSize: '40px 40px'
              }}></div>
            </div>

            <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="text-center mb-20"
              >
                <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-indigo-100 to-blue-100 text-indigo-800 text-sm font-medium mb-6">
                  <span className="w-2 h-2 bg-indigo-500 rounded-full mr-2"></span>
                  Real Examples
                </div>
                <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent mb-6">
                  See It In Action
                </h2>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                  Real compatibility scenarios showing how our tool prevents costly mistakes and helps you make informed decisions.
                </p>
              </motion.div>

              <div className="grid lg:grid-cols-2 gap-8 lg:gap-12">
                <motion.div
                  initial={{ opacity: 0, x: -30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8 }}
                  viewport={{ once: true }}
                  whileHover={{ y: -5, transition: { duration: 0.3 } }}
                >
                  <div className="relative p-8 bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-300 border border-gray-100 overflow-hidden">
                    {/* Background Gradient */}
                    <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full -translate-y-16 translate-x-16 opacity-50"></div>

                    {/* Device Icons */}
                    <div className="relative flex items-center justify-center space-x-6 mb-8">
                      <div className="relative">
                        <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg">
                          <Smartphone className="text-white" size={28} />
                        </div>
                        <div className="absolute -bottom-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                          <CheckCircle className="text-white" size={14} />
                        </div>
                      </div>

                      <div className="text-3xl font-bold text-gray-300">+</div>

                      <div className="relative">
                        <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                          <Headphones className="text-white" size={28} />
                        </div>
                        <div className="absolute -bottom-2 -right-2 w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center">
                          <AlertTriangle className="text-white" size={14} />
                        </div>
                      </div>
                    </div>

                    <div className="text-center mb-6">
                      <h3 className="text-xl font-bold text-gray-900 mb-2">iPhone 15 Pro + Sony WF-1000XM4</h3>
                      <div className="inline-flex items-center px-3 py-1 rounded-full bg-yellow-100 text-yellow-800 text-sm font-medium">
                        75% Compatible
                      </div>
                    </div>

                    <div className="space-y-3 mb-6">
                      <div className="flex items-center justify-between p-3 bg-green-50 rounded-xl">
                        <div className="flex items-center">
                          <CheckCircle className="text-green-600 mr-2" size={16} />
                          <span className="text-sm font-medium text-green-800">AAC Codec</span>
                        </div>
                        <Badge variant="success" className="text-xs">Works</Badge>
                      </div>

                      <div className="flex items-center justify-between p-3 bg-red-50 rounded-xl">
                        <div className="flex items-center">
                          <XCircle className="text-red-600 mr-2" size={16} />
                          <span className="text-sm font-medium text-red-800">LDAC Support</span>
                        </div>
                        <Badge variant="error" className="text-xs">Missing</Badge>
                      </div>

                      <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-xl">
                        <div className="flex items-center">
                          <AlertTriangle className="text-yellow-600 mr-2" size={16} />
                          <span className="text-sm font-medium text-yellow-800">Hi-Res Audio</span>
                        </div>
                        <Badge variant="warning" className="text-xs">Limited</Badge>
                      </div>
                    </div>

                    <div className="p-4 bg-gray-50 rounded-xl">
                      <p className="text-sm text-gray-600 leading-relaxed">
                        <strong className="text-gray-900">Result:</strong> Great sound quality with AAC, but you won't get LDAC's hi-res audio benefits. Consider alternatives for audiophile experience.
                      </p>
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: 30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                  viewport={{ once: true }}
                  whileHover={{ y: -5, transition: { duration: 0.3 } }}
                >
                  <div className="relative p-8 bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-300 border border-gray-100 overflow-hidden">
                    {/* Background Gradient */}
                    <div className="absolute top-0 left-0 w-32 h-32 bg-gradient-to-br from-green-100 to-emerald-100 rounded-full -translate-y-16 -translate-x-16 opacity-50"></div>

                    {/* Device Icons */}
                    <div className="relative flex items-center justify-center space-x-6 mb-8">
                      <div className="relative">
                        <div className="w-16 h-16 bg-gradient-to-r from-gray-600 to-gray-700 rounded-2xl flex items-center justify-center shadow-lg">
                          <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M20 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4V8h16v10z"/>
                          </svg>
                        </div>
                        <div className="absolute -bottom-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                          <CheckCircle className="text-white" size={14} />
                        </div>
                      </div>

                      <div className="text-3xl font-bold text-gray-300">+</div>

                      <div className="relative">
                        <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg">
                          <HardDrive className="text-white" size={28} />
                        </div>
                        <div className="absolute -bottom-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                          <CheckCircle className="text-white" size={14} />
                        </div>
                      </div>
                    </div>

                    <div className="text-center mb-6">
                      <h3 className="text-xl font-bold text-gray-900 mb-2">MacBook Pro M3 + Samsung 980 PRO</h3>
                      <div className="inline-flex items-center px-3 py-1 rounded-full bg-green-100 text-green-800 text-sm font-medium">
                        100% Compatible
                      </div>
                    </div>

                    <div className="space-y-3 mb-6">
                      <div className="flex items-center justify-between p-3 bg-green-50 rounded-xl">
                        <div className="flex items-center">
                          <CheckCircle className="text-green-600 mr-2" size={16} />
                          <span className="text-sm font-medium text-green-800">NVMe Interface</span>
                        </div>
                        <Badge variant="success" className="text-xs">Perfect</Badge>
                      </div>

                      <div className="flex items-center justify-between p-3 bg-green-50 rounded-xl">
                        <div className="flex items-center">
                          <CheckCircle className="text-green-600 mr-2" size={16} />
                          <span className="text-sm font-medium text-green-800">PCIe 4.0 Support</span>
                        </div>
                        <Badge variant="success" className="text-xs">Supported</Badge>
                      </div>

                      <div className="flex items-center justify-between p-3 bg-green-50 rounded-xl">
                        <div className="flex items-center">
                          <CheckCircle className="text-green-600 mr-2" size={16} />
                          <span className="text-sm font-medium text-green-800">Full Speed (7GB/s)</span>
                        </div>
                        <Badge variant="success" className="text-xs">Maximum</Badge>
                      </div>
                    </div>

                    <div className="p-4 bg-green-50 rounded-xl">
                      <p className="text-sm text-gray-600 leading-relaxed">
                        <strong className="text-gray-900">Result:</strong> Perfect match! You'll get the full 7GB/s read speeds for lightning-fast performance. Ideal for video editing and large file transfers.
                      </p>
                    </div>
                  </div>
                </motion.div>
              </div>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                viewport={{ once: true }}
                className="text-center mt-16"
              >
                <div className="relative inline-block">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur-lg opacity-30 animate-pulse"></div>
                  <Button
                    size="lg"
                    onClick={() => setShowChecker(true)}
                    className="relative bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg font-semibold shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105"
                  >
                    Try It Yourself - It's Free
                    <ArrowRight className="ml-2" size={20} />
                  </Button>
                </div>
                <p className="text-sm text-gray-500 mt-4">No signup required • Instant results</p>
              </motion.div>
            </div>
          </section>
        </div>
      ) : (
        // Compatibility Checker
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
          <div className="bg-white/80 backdrop-blur-md shadow-lg border-b border-gray-200/50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6 }}
                >
                  <div className="flex items-center space-x-3 mb-4 lg:mb-0">
                    <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center">
                      <Search className="text-white" size={20} />
                    </div>
                    <div>
                      <h1 className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                        Compatibility Checker
                      </h1>
                      <p className="text-lg text-gray-600">
                        Get instant compatibility analysis for your tech purchases
                      </p>
                    </div>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                >
                  <Button
                    variant="outline"
                    onClick={() => {
                      setShowChecker(false);
                      setCompatibilityResult(null);
                      setSelectedDevice(null);
                      setSelectedAccessory(null);
                    }}
                    className="border-gray-300 hover:bg-gray-50 px-6 py-3"
                  >
                    ← Back to Home
                  </Button>
                </motion.div>
              </div>
            </div>
          </div>

          <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {!compatibilityResult ? (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="grid lg:grid-cols-2 gap-8"
              >
                {/* Device Selection */}
                <Card>
                  <CardContent className="p-6">
                    <DeviceSelector
                      onDeviceSelect={setSelectedDevice}
                      selectedDevice={selectedDevice}
                    />
                  </CardContent>
                </Card>

                {/* Accessory Selection */}
                <Card>
                  <CardContent className="p-6">
                    <AccessorySelector
                      onAccessorySelect={setSelectedAccessory}
                      selectedAccessory={selectedAccessory}
                    />
                  </CardContent>
                </Card>

                {/* Check Compatibility Button */}
                {selectedDevice && selectedAccessory && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3 }}
                    className="lg:col-span-2 text-center"
                  >
                    <Button
                      onClick={checkCompatibility}
                      loading={loading}
                      size="lg"
                      className="px-8 bg-blue-600 hover:bg-blue-700"
                    >
                      <Search className="mr-2" size={20} />
                      Check Compatibility
                    </Button>
                  </motion.div>
                )}
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <Card>
                  <CardContent className="p-6">
                    <div className="mb-6 text-center">
                      <Button
                        onClick={resetSelection}
                        variant="outline"
                        size="sm"
                      >
                        ← Check Another Combination
                      </Button>
                    </div>
                    <CompatibilityResult data={compatibilityResult} />
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </main>
        </div>
      )}

    </div>
  );
}
