'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { CheckCircle, XCircle, AlertTriangle, Lightbulb, ExternalLink } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/Card';
import { Badge } from './ui/Badge';
import { Button } from './ui/Button';

interface CompatibilityData {
  device: {
    name: string;
    brand: string;
    type: string;
  };
  accessory: {
    name: string;
    brand: string;
    type: string;
    price?: {
      amount: number;
      currency: string;
    };
  };
  compatibility: {
    compatible: boolean;
    compatibleFeatures: string[];
    incompatibleFeatures: string[];
    warnings: string[];
    suggestions: string[];
    score: number;
  };
  suggestions?: Array<{
    _id: string;
    name: string;
    brand: string;
    price?: {
      amount: number;
      currency: string;
    };
  }>;
}

interface CompatibilityResultProps {
  data: CompatibilityData;
}

export const CompatibilityResult: React.FC<CompatibilityResultProps> = ({ data }) => {
  const { device, accessory, compatibility, suggestions } = data;
  const [aiExplanation, setAiExplanation] = React.useState<string>('');
  const [loadingAI, setLoadingAI] = React.useState(false);
  const [showAI, setShowAI] = React.useState(false);
  
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBg = (score: number) => {
    if (score >= 80) return 'bg-green-100';
    if (score >= 60) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  const generateAIExplanation = async () => {
    setLoadingAI(true);
    try {
      const response = await fetch('/api/ai-explanation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          deviceName: device.name,
          deviceBrand: device.brand,
          deviceType: device.type,
          deviceSpecs: {}, // Would include full device specs in real implementation
          accessoryName: accessory.name,
          accessoryBrand: accessory.brand,
          accessoryType: accessory.type,
          accessorySpecs: {}, // Would include full accessory specs in real implementation
          compatibilityResult: compatibility
        }),
      });

      const result = await response.json();
      setAiExplanation(result.explanation);
      setShowAI(true);
    } catch (error) {
      console.error('Error getting AI explanation:', error);
      setAiExplanation('Unable to generate AI explanation at this time.');
      setShowAI(true);
    } finally {
      setLoadingAI(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Compatibility Analysis
        </h2>
        <p className="text-gray-600">
          {device.brand} {device.name} + {accessory.brand} {accessory.name}
        </p>
      </div>

      {/* Compatibility Score */}
      <Card className={`border-2 ${compatibility.score >= 80 ? 'border-green-200 bg-green-50' : compatibility.score >= 60 ? 'border-yellow-200 bg-yellow-50' : 'border-red-200 bg-red-50'}`}>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                Compatibility Score
              </h3>
              <p className="text-sm text-gray-600">
                {compatibility.compatible ? 'Compatible' : 'Limited Compatibility'}
              </p>
            </div>
            <div className="text-center">
              <div className={`text-4xl font-bold ${getScoreColor(compatibility.score)}`}>
                {compatibility.score}%
              </div>
              <Badge variant={compatibility.score >= 80 ? 'success' : compatibility.score >= 60 ? 'warning' : 'error'}>
                {compatibility.score >= 80 ? 'Excellent' : compatibility.score >= 60 ? 'Good' : 'Limited'}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Compatible Features */}
      {compatibility.compatibleFeatures.length > 0 && (
        <div>
          <Card className="border-green-200">
            <CardHeader className="pb-3">
              <div className="flex items-center">
                <CheckCircle className="text-green-600 mr-2" size={20} />
                <CardTitle className="text-green-800 text-lg">What Works Perfectly</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {compatibility.compatibleFeatures.map((feature, index) => (
                  <div
                    key={index}
                    className="flex items-center"
                  >
                    <Badge variant="success" className="mr-2">
                      <CheckCircle size={12} className="mr-1" />
                      Compatible
                    </Badge>
                    <span className="text-sm text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Incompatible Features */}
      {compatibility.incompatibleFeatures.length > 0 && (
        <div className="bg-red-50 p-4 rounded-lg">
          <div className="flex items-center mb-3">
            <XCircle className="text-red-600 mr-2" size={20} />
            <h4 className="font-semibold text-red-800">What Doesn't Work</h4>
          </div>
          <ul className="space-y-1">
            {compatibility.incompatibleFeatures.map((feature, index) => (
              <li key={index} className="text-red-700 text-sm flex items-center">
                <span className="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                {feature}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Warnings */}
      {compatibility.warnings.length > 0 && (
        <div className="bg-yellow-50 p-4 rounded-lg">
          <div className="flex items-center mb-3">
            <AlertTriangle className="text-yellow-600 mr-2" size={20} />
            <h4 className="font-semibold text-yellow-800">Important Notes</h4>
          </div>
          <ul className="space-y-1">
            {compatibility.warnings.map((warning, index) => (
              <li key={index} className="text-yellow-700 text-sm flex items-center">
                <span className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
                {warning}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Suggestions */}
      {compatibility.suggestions.length > 0 && (
        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="flex items-center mb-3">
            <Lightbulb className="text-blue-600 mr-2" size={20} />
            <h4 className="font-semibold text-blue-800">Recommendations</h4>
          </div>
          <ul className="space-y-1">
            {compatibility.suggestions.map((suggestion, index) => (
              <li key={index} className="text-blue-700 text-sm flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                {suggestion}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* AI Explanation Section */}
      <div>
        <Card className="border-blue-200">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Lightbulb className="text-blue-600 mr-2" size={20} />
                <CardTitle className="text-blue-800 text-lg">AI Expert Analysis</CardTitle>
              </div>
              {!showAI && (
                <Button
                  onClick={generateAIExplanation}
                  loading={loadingAI}
                  variant="outline"
                  size="sm"
                >
                  Get AI Explanation
                </Button>
              )}
            </div>
          </CardHeader>
          {showAI && (
            <CardContent>
              <div className="prose prose-sm max-w-none">
                <p className="text-gray-700 leading-relaxed whitespace-pre-line">
                  {aiExplanation}
                </p>
              </div>
            </CardContent>
          )}
        </Card>
      </div>

      {/* Buy Links Section */}
      <div>
        <Card className="border-green-200 bg-green-50">
          <CardHeader>
            <CardTitle className="text-green-800 text-lg flex items-center">
              <ExternalLink className="mr-2" size={20} />
              Where to Buy
            </CardTitle>
            <CardDescription>
              {compatibility.compatible ?
                "Ready to purchase? Here are the best places to buy:" :
                "Consider these alternatives for better compatibility:"
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-4">
              <Button
                variant="outline"
                className="w-full justify-between"
                onClick={() => window.open(`https://amazon.com/s?k=${encodeURIComponent(accessory.brand + ' ' + accessory.name)}`, '_blank')}
              >
                <span>Amazon</span>
                <ExternalLink size={16} />
              </Button>
              <Button
                variant="outline"
                className="w-full justify-between"
                onClick={() => window.open(`https://flipkart.com/search?q=${encodeURIComponent(accessory.brand + ' ' + accessory.name)}`, '_blank')}
              >
                <span>Flipkart</span>
                <ExternalLink size={16} />
              </Button>
            </div>
            {accessory.price && (
              <div className="mt-4 p-3 bg-white rounded-lg border">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Expected Price Range:</span>
                  <span className="font-semibold text-lg text-green-600">
                    ${accessory.price.amount} {accessory.price.currency}
                  </span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Alternative Suggestions */}
      {suggestions && suggestions.length > 0 && (
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="text-gray-800">Better Alternatives</CardTitle>
              <CardDescription>
                These products might offer better compatibility with your device
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                {suggestions.map((suggestion) => (
                  <div key={suggestion._id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h5 className="font-medium text-gray-900 mb-1">
                          {suggestion.brand} {suggestion.name}
                        </h5>
                        {suggestion.price && (
                          <p className="text-sm text-gray-600 mb-3">
                            ${suggestion.price.amount} {suggestion.price.currency}
                          </p>
                        )}
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => window.open(`https://amazon.com/s?k=${encodeURIComponent(suggestion.brand + ' ' + suggestion.name)}`, '_blank')}
                          >
                            Amazon
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => window.open(`https://flipkart.com/search?q=${encodeURIComponent(suggestion.brand + ' ' + suggestion.name)}`, '_blank')}
                          >
                            Flipkart
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};
