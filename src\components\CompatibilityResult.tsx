'use client';

import React from 'react';
import { CheckCircle, XCircle, AlertTriangle, Lightbulb } from 'lucide-react';

interface CompatibilityData {
  device: {
    name: string;
    brand: string;
    type: string;
  };
  accessory: {
    name: string;
    brand: string;
    type: string;
    price?: {
      amount: number;
      currency: string;
    };
  };
  compatibility: {
    compatible: boolean;
    compatibleFeatures: string[];
    incompatibleFeatures: string[];
    warnings: string[];
    suggestions: string[];
    score: number;
  };
  suggestions?: Array<{
    _id: string;
    name: string;
    brand: string;
    price?: {
      amount: number;
      currency: string;
    };
  }>;
}

interface CompatibilityResultProps {
  data: CompatibilityData;
}

export const CompatibilityResult: React.FC<CompatibilityResultProps> = ({ data }) => {
  const { device, accessory, compatibility, suggestions } = data;
  
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBg = (score: number) => {
    if (score >= 80) return 'bg-green-100';
    if (score >= 60) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Compatibility Check
        </h2>
        <p className="text-gray-600">
          {device.brand} {device.name} + {accessory.brand} {accessory.name}
        </p>
      </div>

      {/* Compatibility Score */}
      <div className={`p-6 rounded-lg ${getScoreBg(compatibility.score)}`}>
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Compatibility Score
            </h3>
            <p className="text-sm text-gray-600">
              {compatibility.compatible ? 'Compatible' : 'Limited Compatibility'}
            </p>
          </div>
          <div className={`text-3xl font-bold ${getScoreColor(compatibility.score)}`}>
            {compatibility.score}%
          </div>
        </div>
      </div>

      {/* Compatible Features */}
      {compatibility.compatibleFeatures.length > 0 && (
        <div className="bg-green-50 p-4 rounded-lg">
          <div className="flex items-center mb-3">
            <CheckCircle className="text-green-600 mr-2" size={20} />
            <h4 className="font-semibold text-green-800">What Works</h4>
          </div>
          <ul className="space-y-1">
            {compatibility.compatibleFeatures.map((feature, index) => (
              <li key={index} className="text-green-700 text-sm flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                {feature}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Incompatible Features */}
      {compatibility.incompatibleFeatures.length > 0 && (
        <div className="bg-red-50 p-4 rounded-lg">
          <div className="flex items-center mb-3">
            <XCircle className="text-red-600 mr-2" size={20} />
            <h4 className="font-semibold text-red-800">What Doesn't Work</h4>
          </div>
          <ul className="space-y-1">
            {compatibility.incompatibleFeatures.map((feature, index) => (
              <li key={index} className="text-red-700 text-sm flex items-center">
                <span className="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                {feature}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Warnings */}
      {compatibility.warnings.length > 0 && (
        <div className="bg-yellow-50 p-4 rounded-lg">
          <div className="flex items-center mb-3">
            <AlertTriangle className="text-yellow-600 mr-2" size={20} />
            <h4 className="font-semibold text-yellow-800">Important Notes</h4>
          </div>
          <ul className="space-y-1">
            {compatibility.warnings.map((warning, index) => (
              <li key={index} className="text-yellow-700 text-sm flex items-center">
                <span className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
                {warning}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Suggestions */}
      {compatibility.suggestions.length > 0 && (
        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="flex items-center mb-3">
            <Lightbulb className="text-blue-600 mr-2" size={20} />
            <h4 className="font-semibold text-blue-800">Recommendations</h4>
          </div>
          <ul className="space-y-1">
            {compatibility.suggestions.map((suggestion, index) => (
              <li key={index} className="text-blue-700 text-sm flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                {suggestion}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Alternative Suggestions */}
      {suggestions && suggestions.length > 0 && (
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-semibold text-gray-800 mb-3">
            Better Alternatives
          </h4>
          <div className="grid gap-3">
            {suggestions.map((suggestion) => (
              <div key={suggestion._id} className="bg-white p-3 rounded border">
                <div className="flex justify-between items-center">
                  <div>
                    <h5 className="font-medium text-gray-900">
                      {suggestion.brand} {suggestion.name}
                    </h5>
                  </div>
                  {suggestion.price && (
                    <span className="text-sm font-medium text-gray-600">
                      ${suggestion.price.amount}
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
