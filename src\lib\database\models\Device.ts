import mongoose, { Schema, Document } from 'mongoose';

export interface IDevice extends Document {
  name: string;
  brand: string;
  type: 'smartphone' | 'laptop' | 'tablet';
  specs: {
    bluetooth?: string;
    supportedCodecs?: string[];
    usbType?: string;
    dac?: string;
    storageInterface?: string[];
    maxStorageCapacity?: string;
    powerDelivery?: string;
    displayPorts?: string[];
    hdmiVersion?: string;
    refreshRateSupport?: number[];
  };
  releaseYear?: number;
  imageUrl?: string;
  createdAt: Date;
  updatedAt: Date;
}

const DeviceSchema = new Schema<IDevice>({
  name: { type: String, required: true },
  brand: { type: String, required: true },
  type: { 
    type: String, 
    required: true, 
    enum: ['smartphone', 'laptop', 'tablet'] 
  },
  specs: {
    bluetooth: String,
    supportedCodecs: [String],
    usbType: String,
    dac: String,
    storageInterface: [String],
    maxStorageCapacity: String,
    powerDelivery: String,
    displayPorts: [String],
    hdmiVersion: String,
    refreshRateSupport: [Number]
  },
  releaseYear: Number,
  imageUrl: String
}, {
  timestamps: true
});

DeviceSchema.index({ name: 1, brand: 1 });
DeviceSchema.index({ type: 1 });

export default mongoose.models.Device || mongoose.model<IDevice>('Device', DeviceSchema);
