# Product Compatibility Checker

A modern web application that helps users determine if tech products (TWS earbuds, SSDs, chargers, cables, monitors, soundbars) are fully compatible with their devices (smartphones, laptops, tablets).

## Features

- **Device Selection**: Choose from smartphones, laptops, and tablets
- **Accessory Compatibility**: Check compatibility with various accessories
- **Detailed Analysis**: Get comprehensive compatibility reports with:
  - Compatible features
  - Incompatible features
  - Warnings and recommendations
  - Compatibility score (0-100%)
- **Smart Suggestions**: Get alternative product recommendations
- **Clean UI**: Modern, responsive interface built with Tailwind CSS

## Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes
- **Database**: MongoDB with Mongoose
- **Icons**: Lucide React
- **Deployment**: Vercel (recommended)

## Getting Started

### Prerequisites

- Node.js 18+
- MongoDB (local or cloud instance)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd productfinder
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.local.example .env.local
```

Edit `.env.local` with your MongoDB connection string:
```
MONGODB_URI=mongodb://localhost:27017/productfinder
NEXTAUTH_SECRET=your-secret-key-here
NEXTAUTH_URL=http://localhost:3000
```

4. Start MongoDB (if running locally):
```bash
# On macOS with Homebrew
brew services start mongodb-community

# On Windows
net start MongoDB

# On Linux
sudo systemctl start mongod
```

5. Seed the database with sample data:
```bash
npm run seed
```

6. Start the development server:
```bash
npm run dev
```

7. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

```
src/
├── app/                    # Next.js app directory
│   ├── api/               # API routes
│   │   ├── devices/       # Device management
│   │   ├── accessories/   # Accessory management
│   │   └── check-compatibility/ # Compatibility checking
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx          # Home page
├── components/            # React components
│   ├── ui/               # Reusable UI components
│   ├── DeviceSelector.tsx
│   ├── AccessorySelector.tsx
│   └── CompatibilityResult.tsx
└── lib/                  # Utilities and configurations
    ├── database/         # Database models and connection
    │   ├── models/       # Mongoose models
    │   └── connection.ts # Database connection
    └── compatibility/    # Compatibility logic
        └── engine.ts     # Compatibility checking engine
```

## API Endpoints

- `GET /api/devices` - Get devices (with optional type and search filters)
- `POST /api/devices` - Create a new device
- `GET /api/accessories` - Get accessories (with optional type and search filters)
- `POST /api/accessories` - Create a new accessory
- `POST /api/check-compatibility` - Check compatibility between device and accessory

## Compatibility Logic

The app supports compatibility checking for:

- **TWS Earbuds**: Bluetooth version, audio codecs (AAC, SBC, LDAC, etc.)
- **SSDs**: Storage interface (NVMe, SATA), capacity, speed
- **Chargers**: Power delivery, connector type, wattage
- **Cables**: Connector compatibility, data transfer capabilities
- **Monitors**: Display ports, refresh rate, resolution support
- **Soundbars**: HDMI ARC/eARC, audio format support

## Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run seed` - Seed database with sample data

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
