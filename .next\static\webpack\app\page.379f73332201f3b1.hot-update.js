"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_NoSSR__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/NoSSR */ \"(app-pages-browser)/./src/components/NoSSR.tsx\");\n/* harmony import */ var _components_DeviceSelector__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/DeviceSelector */ \"(app-pages-browser)/./src/components/DeviceSelector.tsx\");\n/* harmony import */ var _components_AccessorySelector__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/AccessorySelector */ \"(app-pages-browser)/./src/components/AccessorySelector.tsx\");\n/* harmony import */ var _components_CompatibilityResult__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/CompatibilityResult */ \"(app-pages-browser)/./src/components/CompatibilityResult.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/Badge */ \"(app-pages-browser)/./src/components/ui/Badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/headphones.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [selectedDevice, setSelectedDevice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedAccessory, setSelectedAccessory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [compatibilityResult, setCompatibilityResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showChecker, setShowChecker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const checkCompatibility = async ()=>{\n        if (!selectedDevice || !selectedAccessory) return;\n        setLoading(true);\n        try {\n            const response = await fetch('/api/check-compatibility', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    deviceId: selectedDevice._id,\n                    accessoryId: selectedAccessory._id\n                })\n            });\n            const data = await response.json();\n            setCompatibilityResult(data);\n        } catch (error) {\n            console.error('Error checking compatibility:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const resetSelection = ()=>{\n        setSelectedDevice(null);\n        setSelectedAccessory(null);\n        setCompatibilityResult(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NoSSR__WEBPACK_IMPORTED_MODULE_2__.NoSSR, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\",\n            children: !showChecker ? // Landing Page\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"relative overflow-hidden bg-gradient-to-br from-indigo-900 via-blue-900 to-purple-900\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 opacity-20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0\",\n                                    style: {\n                                        backgroundImage: \"radial-gradient(circle at 25px 25px, rgba(255,255,255,0.1) 2px, transparent 0)\",\n                                        backgroundSize: '50px 50px'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white/90 text-sm font-medium mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Save Money • Avoid Incompatible Purchases\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-5xl md:text-7xl font-extrabold mb-8 bg-gradient-to-r from-white via-blue-100 to-purple-200 bg-clip-text text-transparent leading-tight\",\n                                            children: [\n                                                \"Don't Waste Money on Features\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent\",\n                                                    children: \"You Can't Use\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl md:text-2xl mb-12 text-blue-100 max-w-4xl mx-auto leading-relaxed\",\n                                            children: \"Get instant compatibility analysis for your tech purchases. Know exactly what features will work with your devices before you buy.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-4 justify-center items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                    size: \"lg\",\n                                                    onClick: ()=>setShowChecker(true),\n                                                    className: \"bg-gradient-to-r from-yellow-400 to-orange-500 text-black hover:from-yellow-300 hover:to-orange-400 text-lg px-8 py-4 font-semibold shadow-2xl hover:shadow-yellow-500/25 transition-all duration-300 transform hover:scale-105\",\n                                                    children: [\n                                                        \"Check Compatibility Now\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"ml-2\",\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 113,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"lg\",\n                                                    className: \"border-white/30 text-white hover:bg-white/10 backdrop-blur-sm px-8 py-4\",\n                                                    onClick: ()=>{\n                                                        var _document_getElementById;\n                                                        (_document_getElementById = document.getElementById('how-it-works')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                                                            behavior: 'smooth'\n                                                        });\n                                                    },\n                                                    children: \"Learn How It Works\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-3 gap-8 mt-16 max-w-2xl mx-auto\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-3xl font-bold text-white mb-2\",\n                                                            children: \"1000+\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-blue-200 text-sm\",\n                                                            children: \"Devices Supported\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 132,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-3xl font-bold text-white mb-2\",\n                                                            children: \"95%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-blue-200 text-sm\",\n                                                            children: \"Accuracy Rate\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 136,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-3xl font-bold text-white mb-2\",\n                                                            children: \"$500+\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 139,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-blue-200 text-sm\",\n                                                            children: \"Average Savings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-20 left-10 lg:left-20 animate-bounce\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: 32,\n                                                className: \"text-blue-300\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-32 right-10 lg:right-20 animate-pulse\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                size: 28,\n                                                className: \"text-purple-300\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-32 left-16 lg:left-32 animate-bounce\",\n                                        style: {\n                                            animationDelay: '1s'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                size: 30,\n                                                className: \"text-green-300\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-20 right-16 lg:right-32 animate-pulse\",\n                                        style: {\n                                            animationDelay: '2s'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                size: 26,\n                                                className: \"text-yellow-300\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"how-it-works\",\n                        className: \"py-24 bg-white relative overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 left-0 w-full h-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-20 left-10 w-72 h-72 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-40 right-10 w-72 h-72 bg-purple-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse\",\n                                        style: {\n                                            animationDelay: '2s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-20 left-1/2 w-72 h-72 bg-indigo-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse\",\n                                        style: {\n                                            animationDelay: '4s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        className: \"text-center mb-20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 text-sm font-medium mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"w-2 h-2 bg-blue-500 rounded-full mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Simple Process\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent mb-6\",\n                                                children: \"How It Works\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\",\n                                                children: \"Get detailed compatibility analysis in just 3 simple steps. Our AI-powered system analyzes thousands of device specifications instantly.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid md:grid-cols-3 gap-8 lg:gap-12\",\n                                        children: [\n                                            {\n                                                step: \"01\",\n                                                title: \"Select Your Device\",\n                                                description: \"Choose your smartphone, laptop, or tablet from our comprehensive database of 1000+ devices\",\n                                                icon: _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                                gradient: \"from-blue-500 to-cyan-500\",\n                                                bgGradient: \"from-blue-50 to-cyan-50\"\n                                            },\n                                            {\n                                                step: \"02\",\n                                                title: \"Pick an Accessory\",\n                                                description: \"Select the accessory you want to check - TWS earbuds, SSD, charger, cable, monitor, or soundbar\",\n                                                icon: _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                                gradient: \"from-purple-500 to-pink-500\",\n                                                bgGradient: \"from-purple-50 to-pink-50\"\n                                            },\n                                            {\n                                                step: \"03\",\n                                                title: \"Get Smart Results\",\n                                                description: \"Receive detailed compatibility analysis with AI explanations, recommendations, and direct purchase links\",\n                                                icon: _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                                gradient: \"from-green-500 to-emerald-500\",\n                                                bgGradient: \"from-green-50 to-emerald-50\"\n                                            }\n                                        ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 30\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.8,\n                                                    delay: index * 0.2\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                },\n                                                whileHover: {\n                                                    y: -8,\n                                                    transition: {\n                                                        duration: 0.3\n                                                    }\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative p-8 rounded-3xl bg-gradient-to-br \".concat(item.bgGradient, \" border border-white/50 shadow-xl hover:shadow-2xl transition-all duration-300 h-full group\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute -top-4 -left-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 rounded-2xl bg-gradient-to-r \".concat(item.gradient, \" flex items-center justify-center text-white font-bold text-lg shadow-lg\"),\n                                                                children: item.step\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-20 h-20 rounded-2xl bg-gradient-to-r \".concat(item.gradient, \" flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                size: 36,\n                                                                className: \"text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                                    children: item.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 253,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 leading-relaxed\",\n                                                                    children: item.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 254,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-4 right-4 w-8 h-8 bg-white/20 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute bottom-4 left-4 w-6 h-6 bg-white/10 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-24 bg-gradient-to-br from-gray-50 to-blue-50 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 opacity-30\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0\",\n                                    style: {\n                                        backgroundImage: \"radial-gradient(circle at 20px 20px, rgba(0,0,0,0.02) 10px, transparent 0)\",\n                                        backgroundSize: '40px 40px'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        className: \"text-center mb-20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-indigo-100 to-blue-100 text-indigo-800 text-sm font-medium mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"w-2 h-2 bg-indigo-500 rounded-full mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Real Examples\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent mb-6\",\n                                                children: \"See It In Action\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\",\n                                                children: \"Real compatibility scenarios showing how our tool prevents costly mistakes and helps you make informed decisions.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid lg:grid-cols-2 gap-8 lg:gap-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: -30\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.8\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                },\n                                                whileHover: {\n                                                    y: -5,\n                                                    transition: {\n                                                        duration: 0.3\n                                                    }\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative p-8 bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-300 border border-gray-100 overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full -translate-y-16 translate-x-16 opacity-50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative flex items-center justify-center space-x-6 mb-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                className: \"text-white\",\n                                                                                size: 28\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 313,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 312,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute -bottom-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"text-white\",\n                                                                                size: 14\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 316,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 315,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 311,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-3xl font-bold text-gray-300\",\n                                                                    children: \"+\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 320,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                className: \"text-white\",\n                                                                                size: 28\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 324,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 323,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute -bottom-2 -right-2 w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"text-white\",\n                                                                                size: 14\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 327,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 326,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 322,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center mb-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-bold text-gray-900 mb-2\",\n                                                                    children: \"iPhone 15 Pro + Sony WF-1000XM4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 333,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-flex items-center px-3 py-1 rounded-full bg-yellow-100 text-yellow-800 text-sm font-medium\",\n                                                                    children: \"75% Compatible\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3 mb-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between p-3 bg-green-50 rounded-xl\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                    className: \"text-green-600 mr-2\",\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 342,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium text-green-800\",\n                                                                                    children: \"AAC Codec\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 343,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 341,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                            variant: \"success\",\n                                                                            className: \"text-xs\",\n                                                                            children: \"Works\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 345,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 340,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between p-3 bg-red-50 rounded-xl\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    className: \"text-red-600 mr-2\",\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 350,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium text-red-800\",\n                                                                                    children: \"LDAC Support\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 351,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 349,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                            variant: \"error\",\n                                                                            className: \"text-xs\",\n                                                                            children: \"Missing\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 353,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 348,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between p-3 bg-yellow-50 rounded-xl\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"text-yellow-600 mr-2\",\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 358,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium text-yellow-800\",\n                                                                                    children: \"Hi-Res Audio\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 359,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 357,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                            variant: \"warning\",\n                                                                            className: \"text-xs\",\n                                                                            children: \"Limited\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 361,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 356,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-gray-50 rounded-xl\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 leading-relaxed\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        className: \"text-gray-900\",\n                                                                        children: \"Result:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 367,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" Great sound quality with AAC, but you won't get LDAC's hi-res audio benefits. Consider alternatives for audiophile experience.\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: 30\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.8,\n                                                    delay: 0.2\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                },\n                                                whileHover: {\n                                                    y: -5,\n                                                    transition: {\n                                                        duration: 0.3\n                                                    }\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative p-8 bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-300 border border-gray-100 overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-0 left-0 w-32 h-32 bg-gradient-to-br from-green-100 to-emerald-100 rounded-full -translate-y-16 -translate-x-16 opacity-50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative flex items-center justify-center space-x-6 mb-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-16 h-16 bg-gradient-to-r from-gray-600 to-gray-700 rounded-2xl flex items-center justify-center shadow-lg\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-8 h-8 text-white\",\n                                                                                fill: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    d: \"M20 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4V8h16v10z\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 389,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 388,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 387,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute -bottom-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"text-white\",\n                                                                                size: 14\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 393,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 392,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 386,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-3xl font-bold text-gray-300\",\n                                                                    children: \"+\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 397,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                className: \"text-white\",\n                                                                                size: 28\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 401,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 400,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute -bottom-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"text-white\",\n                                                                                size: 14\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 404,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 403,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 399,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center mb-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-bold text-gray-900 mb-2\",\n                                                                    children: \"MacBook Pro M3 + Samsung 980 PRO\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 410,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-flex items-center px-3 py-1 rounded-full bg-green-100 text-green-800 text-sm font-medium\",\n                                                                    children: \"100% Compatible\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 411,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 409,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3 mb-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between p-3 bg-green-50 rounded-xl\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                    className: \"text-green-600 mr-2\",\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 419,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium text-green-800\",\n                                                                                    children: \"NVMe Interface\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 420,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 418,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                            variant: \"success\",\n                                                                            className: \"text-xs\",\n                                                                            children: \"Perfect\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 422,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 417,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between p-3 bg-green-50 rounded-xl\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                    className: \"text-green-600 mr-2\",\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 427,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium text-green-800\",\n                                                                                    children: \"PCIe 4.0 Support\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 428,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 426,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                            variant: \"success\",\n                                                                            className: \"text-xs\",\n                                                                            children: \"Supported\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 430,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 425,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between p-3 bg-green-50 rounded-xl\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                    className: \"text-green-600 mr-2\",\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 435,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium text-green-800\",\n                                                                                    children: \"Full Speed (7GB/s)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 436,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 434,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                            variant: \"success\",\n                                                                            className: \"text-xs\",\n                                                                            children: \"Maximum\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 438,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 416,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-green-50 rounded-xl\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 leading-relaxed\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        className: \"text-gray-900\",\n                                                                        children: \"Result:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 444,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" Perfect match! You'll get the full 7GB/s read speeds for lightning-fast performance. Ideal for video editing and large file transfers.\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 442,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.4\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        className: \"text-center mt-16\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative inline-block\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur-lg opacity-30 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                        size: \"lg\",\n                                                        onClick: ()=>setShowChecker(true),\n                                                        className: \"relative bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg font-semibold shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105\",\n                                                        children: [\n                                                            \"Try It Yourself - It's Free\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"ml-2\",\n                                                                size: 20\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500 mt-4\",\n                                                children: \"No signup required • Instant results\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 76,\n                columnNumber: 9\n            }, this) : // Compatibility Checker\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/80 backdrop-blur-md shadow-lg border-b border-gray-200/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-4 lg:mb-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"text-white\",\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-3xl lg:text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent\",\n                                                        children: \"Compatibility Checker\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg text-gray-600\",\n                                                        children: \"Get instant compatibility analysis for your tech purchases\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>{\n                                            setShowChecker(false);\n                                            setCompatibilityResult(null);\n                                            setSelectedDevice(null);\n                                            setSelectedAccessory(null);\n                                        },\n                                        className: \"border-gray-300 hover:bg-gray-50 px-6 py-3\",\n                                        children: \"← Back to Home\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 479,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 478,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 477,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                        children: !compatibilityResult ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid lg:grid-cols-2 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-lg p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DeviceSelector__WEBPACK_IMPORTED_MODULE_3__.DeviceSelector, {\n                                        onDeviceSelect: setSelectedDevice,\n                                        selectedDevice: selectedDevice\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-lg p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AccessorySelector__WEBPACK_IMPORTED_MODULE_4__.AccessorySelector, {\n                                        onAccessorySelect: setSelectedAccessory,\n                                        selectedAccessory: selectedAccessory\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 17\n                                }, this),\n                                selectedDevice && selectedAccessory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: checkCompatibility,\n                                        loading: loading,\n                                        size: \"lg\",\n                                        className: \"px-8 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"mr-2\",\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 23\n                                            }, this),\n                                            \"Check Compatibility\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 531,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 512,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: resetSelection,\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        children: \"← Check Another Combination\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 547,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CompatibilityResult__WEBPACK_IMPORTED_MODULE_5__.CompatibilityResult, {\n                                    data: compatibilityResult\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 545,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 510,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 476,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 73,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"lRPJOyAYcgr3wdpYHy6KWuM0LnE=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});