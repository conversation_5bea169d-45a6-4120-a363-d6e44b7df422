"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/DeviceSelector.tsx":
/*!*******************************************!*\
  !*** ./src/components/DeviceSelector.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DeviceSelector: () => (/* binding */ DeviceSelector)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_Badge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/Badge */ \"(app-pages-browser)/./src/components/ui/Badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Laptop_Search_Smartphone_Tablet_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Laptop,Search,Smartphone,Tablet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_Laptop_Search_Smartphone_Tablet_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Laptop,Search,Smartphone,Tablet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/laptop.js\");\n/* harmony import */ var _barrel_optimize_names_Laptop_Search_Smartphone_Tablet_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Laptop,Search,Smartphone,Tablet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tablet.js\");\n/* harmony import */ var _barrel_optimize_names_Laptop_Search_Smartphone_Tablet_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Laptop,Search,Smartphone,Tablet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* __next_internal_client_entry_do_not_use__ DeviceSelector auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst DeviceSelector = (param)=>{\n    let { onDeviceSelect, selectedDevice } = param;\n    _s();\n    const [deviceType, setDeviceType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [devices, setDevices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const deviceTypes = [\n        {\n            value: 'smartphone',\n            label: 'Smartphone',\n            icon: _barrel_optimize_names_Laptop_Search_Smartphone_Tablet_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n        },\n        {\n            value: 'laptop',\n            label: 'Laptop',\n            icon: _barrel_optimize_names_Laptop_Search_Smartphone_Tablet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            value: 'tablet',\n            label: 'Tablet',\n            icon: _barrel_optimize_names_Laptop_Search_Smartphone_Tablet_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DeviceSelector.useEffect\": ()=>{\n            if (deviceType) {\n                fetchDevices(deviceType);\n            } else {\n                setDevices([]);\n                onDeviceSelect(null);\n            }\n        }\n    }[\"DeviceSelector.useEffect\"], [\n        deviceType\n    ]);\n    const fetchDevices = async (type, search)=>{\n        setLoading(true);\n        try {\n            let url = \"/api/devices?type=\".concat(type);\n            if (search) {\n                url += \"&search=\".concat(encodeURIComponent(search));\n            }\n            const response = await fetch(url);\n            const data = await response.json();\n            setDevices(data.devices || []);\n        } catch (error) {\n            console.error('Error fetching devices:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Debounced search\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DeviceSelector.useEffect\": ()=>{\n            if (deviceType) {\n                const timeoutId = setTimeout({\n                    \"DeviceSelector.useEffect.timeoutId\": ()=>{\n                        fetchDevices(deviceType, searchQuery);\n                    }\n                }[\"DeviceSelector.useEffect.timeoutId\"], 300);\n                return ({\n                    \"DeviceSelector.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"DeviceSelector.useEffect\"];\n            }\n        }\n    }[\"DeviceSelector.useEffect\"], [\n        searchQuery,\n        deviceType\n    ]);\n    const handleDeviceChange = (deviceId)=>{\n        const device = devices.find((d)=>d._id === deviceId) || null;\n        onDeviceSelect(device);\n    };\n    const deviceOptions = devices.map((device)=>({\n            value: device._id,\n            label: \"\".concat(device.brand, \" \").concat(device.name),\n            description: \"\".concat(device.type, \" • \").concat(device.specs.bluetooth ? \"Bluetooth \".concat(device.specs.bluetooth) : 'No Bluetooth info')\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-2 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Laptop_Search_Smartphone_Tablet_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"mr-2\",\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Select Your Device\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Choose your smartphone, laptop, or tablet to check compatibility\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-3\",\n                        children: deviceTypes.map((param)=>{\n                            let { value, label, icon: Icon } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setDeviceType(value);\n                                    setSearchQuery('');\n                                    onDeviceSelect(null);\n                                },\n                                className: \"p-4 border rounded-lg flex flex-col items-center space-y-2 transition-all hover:scale-105 \".concat(deviceType === value ? 'border-blue-500 bg-blue-50 text-blue-700 shadow-md' : 'border-gray-300 hover:border-gray-400 hover:shadow-sm'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: label\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, value, true, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, undefined),\n                    deviceType && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Search for your device\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Laptop_Search_Smartphone_Tablet_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\",\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Search \".concat(deviceType, \"s...\"),\n                                                value: searchQuery,\n                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, undefined),\n                            devices.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Select your device\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: (selectedDevice === null || selectedDevice === void 0 ? void 0 : selectedDevice._id) || '',\n                                        onChange: (e)=>handleDeviceChange(e.target.value),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Choose your device...\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            devices.map((device)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: device._id,\n                                                    children: [\n                                                        device.brand,\n                                                        \" \",\n                                                        device.name\n                                                    ]\n                                                }, device._id, true, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, undefined),\n                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-3 text-sm text-gray-600\",\n                                children: \"Loading devices...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 11\n                    }, undefined),\n                    selectedDevice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start justify-between\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-gray-900 mb-2\",\n                                        children: [\n                                            selectedDevice.brand,\n                                            \" \",\n                                            selectedDevice.name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"text-xs\",\n                                                children: selectedDevice.type\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            selectedDevice.specs.bluetooth && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                                variant: \"outline\",\n                                                className: \"text-xs\",\n                                                children: [\n                                                    \"Bluetooth \",\n                                                    selectedDevice.specs.bluetooth\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            selectedDevice.specs.supportedCodecs && selectedDevice.specs.supportedCodecs.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                                variant: \"outline\",\n                                                className: \"text-xs\",\n                                                children: [\n                                                    selectedDevice.specs.supportedCodecs.length,\n                                                    \" Audio Codecs\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    selectedDevice.specs.supportedCodecs && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-600 mt-2\",\n                                        children: [\n                                            \"Codecs: \",\n                                            selectedDevice.specs.supportedCodecs.join(', ')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\DeviceSelector.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DeviceSelector, \"WZf4CgnFvyTreraul6P130JyPdk=\");\n_c = DeviceSelector;\nvar _c;\n$RefreshReg$(_c, \"DeviceSelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DeviceSelector.tsx\n"));

/***/ })

});