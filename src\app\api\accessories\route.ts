import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/database/connection';
import Accessory from '@/lib/database/models/Accessory';

export async function GET(request: NextRequest) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const search = searchParams.get('search');
    
    let query: any = {};
    
    if (type) {
      query.type = type;
    }
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { brand: { $regex: search, $options: 'i' } }
      ];
    }
    
    const accessories = await Accessory.find(query)
      .select('name brand type specs price imageUrl')
      .sort({ name: 1 })
      .limit(50);
    
    return NextResponse.json({ accessories });
  } catch (error) {
    console.error('Error fetching accessories:', error);
    return NextResponse.json(
      { error: 'Failed to fetch accessories' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectDB();
    
    const body = await request.json();
    const accessory = new Accessory(body);
    await accessory.save();
    
    return NextResponse.json({ accessory }, { status: 201 });
  } catch (error) {
    console.error('Error creating accessory:', error);
    return NextResponse.json(
      { error: 'Failed to create accessory' },
      { status: 500 }
    );
  }
}
