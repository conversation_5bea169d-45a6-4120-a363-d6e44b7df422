import mongoose, { Schema, Document } from 'mongoose';

export interface IAccessory extends Document {
  name: string;
  brand: string;
  type: 'tws' | 'ssd' | 'charger' | 'cable' | 'monitor' | 'soundbar';
  specs: {
    requiredCodecs?: string[];
    supportedCodecs?: string[];
    features?: string[];
    storageInterface?: string;
    capacity?: string;
    readSpeed?: string;
    writeSpeed?: string;
    powerOutput?: string;
    powerDelivery?: string;
    cableType?: string;
    resolution?: string;
    refreshRate?: number[];
    hdmiVersion?: string;
    audioSupport?: string[];
  };
  compatibility: {
    deviceTypes: string[];
    requirements: {
      minBluetoothVersion?: string;
      requiredPorts?: string[];
      minPowerDelivery?: string;
    };
  };
  price?: {
    amount: number;
    currency: string;
  };
  imageUrl?: string;
  buyLinks?: {
    platform: string;
    url: string;
  }[];
  createdAt: Date;
  updatedAt: Date;
}

const AccessorySchema = new Schema<IAccessory>({
  name: { type: String, required: true },
  brand: { type: String, required: true },
  type: { 
    type: String, 
    required: true, 
    enum: ['tws', 'ssd', 'charger', 'cable', 'monitor', 'soundbar'] 
  },
  specs: {
    requiredCodecs: [String],
    supportedCodecs: [String],
    features: [String],
    storageInterface: String,
    capacity: String,
    readSpeed: String,
    writeSpeed: String,
    powerOutput: String,
    powerDelivery: String,
    cableType: String,
    resolution: String,
    refreshRate: [Number],
    hdmiVersion: String,
    audioSupport: [String]
  },
  compatibility: {
    deviceTypes: [String],
    requirements: {
      minBluetoothVersion: String,
      requiredPorts: [String],
      minPowerDelivery: String
    }
  },
  price: {
    amount: Number,
    currency: String
  },
  imageUrl: String,
  buyLinks: [{
    platform: String,
    url: String
  }]
}, {
  timestamps: true
});

AccessorySchema.index({ name: 1, brand: 1 });
AccessorySchema.index({ type: 1 });
AccessorySchema.index({ 'compatibility.deviceTypes': 1 });

export default mongoose.models.Accessory || mongoose.model<IAccessory>('Accessory', AccessorySchema);
