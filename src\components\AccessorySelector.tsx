'use client';

import React, { useState, useEffect } from 'react';
import { Select } from './ui/Select';
import { Headphones, HardDrive, Zap, Cable, Monitor, Speaker } from 'lucide-react';

interface Accessory {
  _id: string;
  name: string;
  brand: string;
  type: string;
  specs: any;
  price?: {
    amount: number;
    currency: string;
  };
  imageUrl?: string;
}

interface AccessorySelectorProps {
  onAccessorySelect: (accessory: Accessory | null) => void;
  selectedAccessory: Accessory | null;
}

export const AccessorySelector: React.FC<AccessorySelectorProps> = ({
  onAccessorySelect,
  selectedAccessory
}) => {
  const [accessoryType, setAccessoryType] = useState<string>('');
  const [accessories, setAccessories] = useState<Accessory[]>([]);
  const [loading, setLoading] = useState(false);

  const accessoryTypes = [
    { value: 'tws', label: 'TWS Earbuds', icon: Headphones },
    { value: 'ssd', label: 'SSD', icon: HardDrive },
    { value: 'charger', label: 'Charger', icon: Zap },
    { value: 'cable', label: 'Cable', icon: Cable },
    { value: 'monitor', label: 'Monitor', icon: Monitor },
    { value: 'soundbar', label: 'Soundbar', icon: Speaker }
  ];

  useEffect(() => {
    if (accessoryType) {
      fetchAccessories(accessoryType);
    } else {
      setAccessories([]);
      onAccessorySelect(null);
    }
  }, [accessoryType]);

  const fetchAccessories = async (type: string) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/accessories?type=${type}`);
      const data = await response.json();
      setAccessories(data.accessories || []);
    } catch (error) {
      console.error('Error fetching accessories:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAccessoryChange = (accessoryId: string) => {
    const accessory = accessories.find(a => a._id === accessoryId) || null;
    onAccessorySelect(accessory);
  };

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-3">
          Select Accessory
        </h3>
        
        {/* Accessory Type Selection */}
        <div className="grid grid-cols-3 gap-3 mb-4">
          {accessoryTypes.map(({ value, label, icon: Icon }) => (
            <button
              key={value}
              onClick={() => setAccessoryType(value)}
              className={`p-4 border rounded-lg flex flex-col items-center space-y-2 transition-colors ${
                accessoryType === value
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
            >
              <Icon size={24} />
              <span className="text-sm font-medium">{label}</span>
            </button>
          ))}
        </div>

        {/* Accessory Selection */}
        {accessoryType && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Choose the accessory
            </label>
            <select
              value={selectedAccessory?._id || ''}
              onChange={(e) => handleAccessoryChange(e.target.value)}
              disabled={loading}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Choose an accessory...</option>
              {accessories.map(accessory => (
                <option key={accessory._id} value={accessory._id}>
                  {accessory.brand} {accessory.name}
                  {accessory.price ? ` - $${accessory.price.amount}` : ''}
                </option>
              ))}
            </select>
          </div>
        )}

        {loading && (
          <div className="flex items-center justify-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-sm text-gray-600">Loading accessories...</span>
          </div>
        )}

        {/* Selected Accessory Info */}
        {selectedAccessory && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-gray-900">
              {selectedAccessory.brand} {selectedAccessory.name}
            </h4>
            {selectedAccessory.price && (
              <p className="text-sm text-gray-600">
                Price: ${selectedAccessory.price.amount} {selectedAccessory.price.currency}
              </p>
            )}
            <p className="text-sm text-gray-500 capitalize">
              Type: {selectedAccessory.type}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
