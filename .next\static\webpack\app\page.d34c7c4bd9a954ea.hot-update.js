"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/CompatibilityResult.tsx":
/*!************************************************!*\
  !*** ./src/components/CompatibilityResult.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CompatibilityResult: () => (/* binding */ CompatibilityResult)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_ExternalLink_Lightbulb_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,ExternalLink,Lightbulb,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_ExternalLink_Lightbulb_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,ExternalLink,Lightbulb,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_ExternalLink_Lightbulb_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,ExternalLink,Lightbulb,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_ExternalLink_Lightbulb_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,ExternalLink,Lightbulb,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_ExternalLink_Lightbulb_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,ExternalLink,Lightbulb,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _ui_Badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/Badge */ \"(app-pages-browser)/./src/components/ui/Badge.tsx\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ CompatibilityResult auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst CompatibilityResult = (param)=>{\n    let { data } = param;\n    _s();\n    const { device, accessory, compatibility, suggestions } = data;\n    const [aiExplanation, setAiExplanation] = react__WEBPACK_IMPORTED_MODULE_1___default().useState('');\n    const [loadingAI, setLoadingAI] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n    const [showAI, setShowAI] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(false);\n    const getScoreColor = (score)=>{\n        if (score >= 80) return 'text-green-600';\n        if (score >= 60) return 'text-yellow-600';\n        return 'text-red-600';\n    };\n    const getScoreBg = (score)=>{\n        if (score >= 80) return 'bg-green-100';\n        if (score >= 60) return 'bg-yellow-100';\n        return 'bg-red-100';\n    };\n    const generateAIExplanation = async ()=>{\n        setLoadingAI(true);\n        try {\n            const response = await fetch('/api/ai-explanation', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    deviceName: device.name,\n                    deviceBrand: device.brand,\n                    deviceType: device.type,\n                    deviceSpecs: {},\n                    accessoryName: accessory.name,\n                    accessoryBrand: accessory.brand,\n                    accessoryType: accessory.type,\n                    accessorySpecs: {},\n                    compatibilityResult: compatibility\n                })\n            });\n            const result = await response.json();\n            setAiExplanation(result.explanation);\n            setShowAI(true);\n        } catch (error) {\n            console.error('Error getting AI explanation:', error);\n            setAiExplanation('Unable to generate AI explanation at this time.');\n            setShowAI(true);\n        } finally{\n            setLoadingAI(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-2\",\n                        children: \"Compatibility Analysis\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: [\n                            device.brand,\n                            \" \",\n                            device.name,\n                            \" + \",\n                            accessory.brand,\n                            \" \",\n                            accessory.name\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-2 \".concat(compatibility.score >= 80 ? 'border-green-200 bg-green-50' : compatibility.score >= 60 ? 'border-yellow-200 bg-yellow-50' : 'border-red-200 bg-red-50'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"Compatibility Score\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: compatibility.compatible ? 'Compatible' : 'Limited Compatibility'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl font-bold \".concat(getScoreColor(compatibility.score)),\n                                        children: [\n                                            compatibility.score,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                        variant: compatibility.score >= 80 ? 'success' : compatibility.score >= 60 ? 'warning' : 'error',\n                                        children: compatibility.score >= 80 ? 'Excellent' : compatibility.score >= 60 ? 'Good' : 'Limited'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, undefined),\n            compatibility.compatibleFeatures.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"border-green-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            className: \"pb-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_ExternalLink_Lightbulb_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"text-green-600 mr-2\",\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-green-800 text-lg\",\n                                        children: \"What Works Perfectly\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: compatibility.compatibleFeatures.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                variant: \"success\",\n                                                className: \"mr-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_ExternalLink_Lightbulb_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        size: 12,\n                                                        className: \"mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    \"Compatible\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-700\",\n                                                children: feature\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                lineNumber: 137,\n                columnNumber: 9\n            }, undefined),\n            compatibility.incompatibleFeatures.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 p-4 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_ExternalLink_Lightbulb_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"text-red-600 mr-2\",\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-semibold text-red-800\",\n                                children: \"What Doesn't Work\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"space-y-1\",\n                        children: compatibility.incompatibleFeatures.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"text-red-700 text-sm flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"w-2 h-2 bg-red-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    feature\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                lineNumber: 167,\n                columnNumber: 9\n            }, undefined),\n            compatibility.warnings.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-50 p-4 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_ExternalLink_Lightbulb_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"text-yellow-600 mr-2\",\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-semibold text-yellow-800\",\n                                children: \"Important Notes\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"space-y-1\",\n                        children: compatibility.warnings.map((warning, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"text-yellow-700 text-sm flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"w-2 h-2 bg-yellow-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    warning\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                lineNumber: 185,\n                columnNumber: 9\n            }, undefined),\n            compatibility.suggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 p-4 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_ExternalLink_Lightbulb_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"text-blue-600 mr-2\",\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-semibold text-blue-800\",\n                                children: \"Recommendations\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"space-y-1\",\n                        children: compatibility.suggestions.map((suggestion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"text-blue-700 text-sm flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"w-2 h-2 bg-blue-500 rounded-full mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    suggestion\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                lineNumber: 203,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"border-blue-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_ExternalLink_Lightbulb_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"text-blue-600 mr-2\",\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"text-blue-800 text-lg\",\n                                                children: \"AI Expert Analysis\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    !showAI && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: generateAIExplanation,\n                                        loading: loadingAI,\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        children: \"Get AI Explanation\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, undefined),\n                        showAI && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"prose prose-sm max-w-none\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 leading-relaxed whitespace-pre-line\",\n                                    children: aiExplanation\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"border-green-200 bg-green-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-green-800 text-lg flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_ExternalLink_Lightbulb_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"mr-2\",\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Where to Buy\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    children: compatibility.compatible ? \"Ready to purchase? Here are the best places to buy:\" : \"Consider these alternatives for better compatibility:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            className: \"w-full justify-between\",\n                                            onClick: ()=>window.open(\"https://amazon.com/s?k=\".concat(encodeURIComponent(accessory.brand + ' ' + accessory.name)), '_blank'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Amazon\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_ExternalLink_Lightbulb_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            className: \"w-full justify-between\",\n                                            onClick: ()=>window.open(\"https://flipkart.com/search?q=\".concat(encodeURIComponent(accessory.brand + ' ' + accessory.name)), '_blank'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Flipkart\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_ExternalLink_Lightbulb_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 13\n                                }, undefined),\n                                accessory.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 p-3 bg-white rounded-lg border\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Expected Price Range:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold text-lg text-green-600\",\n                                                children: [\n                                                    \"$\",\n                                                    accessory.price.amount,\n                                                    \" \",\n                                                    accessory.price.currency\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                lineNumber: 253,\n                columnNumber: 7\n            }, undefined),\n            suggestions && suggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-gray-800\",\n                                    children: \"Better Alternatives\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    children: \"These products might offer better compatibility with your device\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: suggestions.map((suggestion)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-4 hover:shadow-md transition-shadow\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                        className: \"font-medium text-gray-900 mb-1\",\n                                                        children: [\n                                                            suggestion.brand,\n                                                            \" \",\n                                                            suggestion.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    suggestion.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-3\",\n                                                        children: [\n                                                            \"$\",\n                                                            suggestion.price.amount,\n                                                            \" \",\n                                                            suggestion.price.currency\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>window.open(\"https://amazon.com/s?k=\".concat(encodeURIComponent(suggestion.brand + ' ' + suggestion.name)), '_blank'),\n                                                                children: \"Amazon\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                                                lineNumber: 325,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>window.open(\"https://flipkart.com/search?q=\".concat(encodeURIComponent(suggestion.brand + ' ' + suggestion.name)), '_blank'),\n                                                                children: \"Flipkart\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, suggestion._id, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                    lineNumber: 303,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n                lineNumber: 302,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\components\\\\CompatibilityResult.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CompatibilityResult, \"7ZIT3/pz0krINBzoyvKs2dwcPDo=\");\n_c = CompatibilityResult;\nvar _c;\n$RefreshReg$(_c, \"CompatibilityResult\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CompatibilityResult.tsx\n"));

/***/ })

});