"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_DeviceSelector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/DeviceSelector */ \"(app-pages-browser)/./src/components/DeviceSelector.tsx\");\n/* harmony import */ var _components_AccessorySelector__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AccessorySelector */ \"(app-pages-browser)/./src/components/AccessorySelector.tsx\");\n/* harmony import */ var _components_CompatibilityResult__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/CompatibilityResult */ \"(app-pages-browser)/./src/components/CompatibilityResult.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Badge */ \"(app-pages-browser)/./src/components/ui/Badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/headphones.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [selectedDevice, setSelectedDevice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedAccessory, setSelectedAccessory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [compatibilityResult, setCompatibilityResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showChecker, setShowChecker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const checkCompatibility = async ()=>{\n        if (!selectedDevice || !selectedAccessory) return;\n        setLoading(true);\n        try {\n            const response = await fetch('/api/check-compatibility', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    deviceId: selectedDevice._id,\n                    accessoryId: selectedAccessory._id\n                })\n            });\n            const data = await response.json();\n            setCompatibilityResult(data);\n        } catch (error) {\n            console.error('Error checking compatibility:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const resetSelection = ()=>{\n        setSelectedDevice(null);\n        setSelectedAccessory(null);\n        setCompatibilityResult(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\",\n        children: !showChecker ? // Landing Page\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"relative overflow-hidden bg-gradient-to-br from-indigo-900 via-blue-900 to-purple-900\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 opacity-20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0\",\n                                style: {\n                                    backgroundImage: \"radial-gradient(circle at 25px 25px, rgba(255,255,255,0.1) 2px, transparent 0)\",\n                                    backgroundSize: '50px 50px'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white/90 text-sm font-medium mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Save Money • Avoid Incompatible Purchases\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-5xl md:text-7xl font-extrabold mb-8 bg-gradient-to-r from-white via-blue-100 to-purple-200 bg-clip-text text-transparent leading-tight\",\n                                        children: [\n                                            \"Don't Waste Money on Features\",\n                                            ' ',\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent\",\n                                                children: \"You Can't Use\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl md:text-2xl mb-12 text-blue-100 max-w-4xl mx-auto leading-relaxed\",\n                                        children: \"Get instant compatibility analysis for your tech purchases. Know exactly what features will work with your devices before you buy.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-4 justify-center items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                size: \"lg\",\n                                                onClick: ()=>setShowChecker(true),\n                                                className: \"bg-gradient-to-r from-yellow-400 to-orange-500 text-black hover:from-yellow-300 hover:to-orange-400 text-lg px-8 py-4 font-semibold shadow-2xl hover:shadow-yellow-500/25 transition-all duration-300 transform hover:scale-105\",\n                                                children: [\n                                                    \"Check Compatibility Now\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"ml-2\",\n                                                        size: 20\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                variant: \"outline\",\n                                                size: \"lg\",\n                                                className: \"border-white/30 text-white hover:bg-white/10 backdrop-blur-sm px-8 py-4\",\n                                                onClick: ()=>{\n                                                    var _document_getElementById;\n                                                    (_document_getElementById = document.getElementById('how-it-works')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                                                        behavior: 'smooth'\n                                                    });\n                                                },\n                                                children: \"Learn How It Works\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-3 gap-8 mt-16 max-w-2xl mx-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-white mb-2\",\n                                                        children: \"1000+\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-blue-200 text-sm\",\n                                                        children: \"Devices Supported\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-white mb-2\",\n                                                        children: \"95%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-blue-200 text-sm\",\n                                                        children: \"Accuracy Rate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl font-bold text-white mb-2\",\n                                                        children: \"$500+\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-blue-200 text-sm\",\n                                                        children: \"Average Savings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                    animate: {\n                                        y: [\n                                            0,\n                                            -30,\n                                            0\n                                        ],\n                                        rotate: [\n                                            0,\n                                            10,\n                                            0\n                                        ],\n                                        scale: [\n                                            1,\n                                            1.1,\n                                            1\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 6,\n                                        repeat: Infinity,\n                                        ease: \"easeInOut\"\n                                    },\n                                    className: \"absolute top-20 left-10 lg:left-20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            size: 32,\n                                            className: \"text-blue-300\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                    animate: {\n                                        y: [\n                                            0,\n                                            25,\n                                            0\n                                        ],\n                                        rotate: [\n                                            0,\n                                            -8,\n                                            0\n                                        ],\n                                        scale: [\n                                            1,\n                                            1.05,\n                                            1\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 5,\n                                        repeat: Infinity,\n                                        ease: \"easeInOut\",\n                                        delay: 1\n                                    },\n                                    className: \"absolute top-32 right-10 lg:right-20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            size: 28,\n                                            className: \"text-purple-300\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                    animate: {\n                                        y: [\n                                            0,\n                                            -20,\n                                            0\n                                        ],\n                                        rotate: [\n                                            0,\n                                            5,\n                                            0\n                                        ],\n                                        scale: [\n                                            1,\n                                            1.08,\n                                            1\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 7,\n                                        repeat: Infinity,\n                                        ease: \"easeInOut\",\n                                        delay: 2\n                                    },\n                                    className: \"absolute bottom-32 left-16 lg:left-32\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            size: 30,\n                                            className: \"text-green-300\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                    animate: {\n                                        y: [\n                                            0,\n                                            18,\n                                            0\n                                        ],\n                                        rotate: [\n                                            0,\n                                            -6,\n                                            0\n                                        ],\n                                        scale: [\n                                            1,\n                                            1.06,\n                                            1\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 4.5,\n                                        repeat: Infinity,\n                                        ease: \"easeInOut\",\n                                        delay: 0.5\n                                    },\n                                    className: \"absolute bottom-20 right-16 lg:right-32\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            size: 26,\n                                            className: \"text-yellow-300\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    id: \"how-it-works\",\n                    className: \"py-24 bg-white relative overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-0 left-0 w-full h-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-20 left-10 w-72 h-72 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-40 right-10 w-72 h-72 bg-purple-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse\",\n                                    style: {\n                                        animationDelay: '2s'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-20 left-1/2 w-72 h-72 bg-indigo-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse\",\n                                    style: {\n                                        animationDelay: '4s'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"text-center mb-20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 text-sm font-medium mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"w-2 h-2 bg-blue-500 rounded-full mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Simple Process\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent mb-6\",\n                                            children: \"How It Works\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\",\n                                            children: \"Get detailed compatibility analysis in just 3 simple steps. Our AI-powered system analyzes thousands of device specifications instantly.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid md:grid-cols-3 gap-8 lg:gap-12\",\n                                    children: [\n                                        {\n                                            step: \"01\",\n                                            title: \"Select Your Device\",\n                                            description: \"Choose your smartphone, laptop, or tablet from our comprehensive database of 1000+ devices\",\n                                            icon: _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                                            gradient: \"from-blue-500 to-cyan-500\",\n                                            bgGradient: \"from-blue-50 to-cyan-50\"\n                                        },\n                                        {\n                                            step: \"02\",\n                                            title: \"Pick an Accessory\",\n                                            description: \"Select the accessory you want to check - TWS earbuds, SSD, charger, cable, monitor, or soundbar\",\n                                            icon: _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                            gradient: \"from-purple-500 to-pink-500\",\n                                            bgGradient: \"from-purple-50 to-pink-50\"\n                                        },\n                                        {\n                                            step: \"03\",\n                                            title: \"Get Smart Results\",\n                                            description: \"Receive detailed compatibility analysis with AI explanations, recommendations, and direct purchase links\",\n                                            icon: _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                            gradient: \"from-green-500 to-emerald-500\",\n                                            bgGradient: \"from-green-50 to-emerald-50\"\n                                        }\n                                    ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8,\n                                                delay: index * 0.2\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            whileHover: {\n                                                y: -8,\n                                                transition: {\n                                                    duration: 0.3\n                                                }\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative p-8 rounded-3xl bg-gradient-to-br \".concat(item.bgGradient, \" border border-white/50 shadow-xl hover:shadow-2xl transition-all duration-300 h-full group\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute -top-4 -left-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 rounded-2xl bg-gradient-to-r \".concat(item.gradient, \" flex items-center justify-center text-white font-bold text-lg shadow-lg\"),\n                                                            children: item.step\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-20 h-20 rounded-2xl bg-gradient-to-r \".concat(item.gradient, \" flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                            size: 36,\n                                                            className: \"text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                                children: item.title\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 leading-relaxed\",\n                                                                children: item.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-4 right-4 w-8 h-8 bg-white/20 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute bottom-4 left-4 w-6 h-6 bg-white/10 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"py-24 bg-gradient-to-br from-gray-50 to-blue-50 relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 opacity-30\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0\",\n                                style: {\n                                    backgroundImage: \"radial-gradient(circle at 20px 20px, rgba(0,0,0,0.02) 10px, transparent 0)\",\n                                    backgroundSize: '40px 40px'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"text-center mb-20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-indigo-100 to-blue-100 text-indigo-800 text-sm font-medium mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"w-2 h-2 bg-indigo-500 rounded-full mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Real Examples\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent mb-6\",\n                                            children: \"See It In Action\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\",\n                                            children: \"Real compatibility scenarios showing how our tool prevents costly mistakes and helps you make informed decisions.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid lg:grid-cols-2 gap-8 lg:gap-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: -30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            whileHover: {\n                                                y: -5,\n                                                transition: {\n                                                    duration: 0.3\n                                                }\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative p-8 bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-300 border border-gray-100 overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full -translate-y-16 translate-x-16 opacity-50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative flex items-center justify-center space-x-6 mb-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                            className: \"text-white\",\n                                                                            size: 28\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 362,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 361,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -bottom-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"text-white\",\n                                                                            size: 14\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 365,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 364,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-3xl font-bold text-gray-300\",\n                                                                children: \"+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"text-white\",\n                                                                            size: 28\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 373,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 372,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -bottom-2 -right-2 w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"text-white\",\n                                                                            size: 14\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 376,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 375,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-bold text-gray-900 mb-2\",\n                                                                children: \"iPhone 15 Pro + Sony WF-1000XM4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"inline-flex items-center px-3 py-1 rounded-full bg-yellow-100 text-yellow-800 text-sm font-medium\",\n                                                                children: \"75% Compatible\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between p-3 bg-green-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                className: \"text-green-600 mr-2\",\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 391,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-green-800\",\n                                                                                children: \"AAC Codec\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 392,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 390,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                        variant: \"success\",\n                                                                        className: \"text-xs\",\n                                                                        children: \"Works\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 394,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 389,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between p-3 bg-red-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"text-red-600 mr-2\",\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 399,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-red-800\",\n                                                                                children: \"LDAC Support\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 400,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 398,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                        variant: \"error\",\n                                                                        className: \"text-xs\",\n                                                                        children: \"Missing\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 402,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between p-3 bg-yellow-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"text-yellow-600 mr-2\",\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 407,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-yellow-800\",\n                                                                                children: \"Hi-Res Audio\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 408,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 406,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                        variant: \"warning\",\n                                                                        className: \"text-xs\",\n                                                                        children: \"Limited\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 410,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 405,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 bg-gray-50 rounded-xl\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 leading-relaxed\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    className: \"text-gray-900\",\n                                                                    children: \"Result:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 416,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" Great sound quality with AAC, but you won't get LDAC's hi-res audio benefits. Consider alternatives for audiophile experience.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                x: 30\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                x: 0\n                                            },\n                                            transition: {\n                                                duration: 0.8,\n                                                delay: 0.2\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            whileHover: {\n                                                y: -5,\n                                                transition: {\n                                                    duration: 0.3\n                                                }\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative p-8 bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-300 border border-gray-100 overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute top-0 left-0 w-32 h-32 bg-gradient-to-br from-green-100 to-emerald-100 rounded-full -translate-y-16 -translate-x-16 opacity-50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative flex items-center justify-center space-x-6 mb-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-16 h-16 bg-gradient-to-r from-gray-600 to-gray-700 rounded-2xl flex items-center justify-center shadow-lg\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-8 h-8 text-white\",\n                                                                            fill: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M20 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4V8h16v10z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 438,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 437,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 436,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -bottom-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"text-white\",\n                                                                            size: 14\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 442,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 441,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-3xl font-bold text-gray-300\",\n                                                                children: \"+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"text-white\",\n                                                                            size: 28\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 450,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 449,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -bottom-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"text-white\",\n                                                                            size: 14\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 453,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 452,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-bold text-gray-900 mb-2\",\n                                                                children: \"MacBook Pro M3 + Samsung 980 PRO\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 459,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"inline-flex items-center px-3 py-1 rounded-full bg-green-100 text-green-800 text-sm font-medium\",\n                                                                children: \"100% Compatible\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3 mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between p-3 bg-green-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                className: \"text-green-600 mr-2\",\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 468,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-green-800\",\n                                                                                children: \"NVMe Interface\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 469,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 467,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                        variant: \"success\",\n                                                                        className: \"text-xs\",\n                                                                        children: \"Perfect\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 471,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between p-3 bg-green-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                className: \"text-green-600 mr-2\",\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 476,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-green-800\",\n                                                                                children: \"PCIe 4.0 Support\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 477,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 475,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                        variant: \"success\",\n                                                                        className: \"text-xs\",\n                                                                        children: \"Supported\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 479,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between p-3 bg-green-50 rounded-xl\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                className: \"text-green-600 mr-2\",\n                                                                                size: 16\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 484,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-green-800\",\n                                                                                children: \"Full Speed (7GB/s)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 485,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 483,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                        variant: \"success\",\n                                                                        className: \"text-xs\",\n                                                                        children: \"Maximum\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 487,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 482,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 bg-green-50 rounded-xl\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 leading-relaxed\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    className: \"text-gray-900\",\n                                                                    children: \"Result:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 493,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" Perfect match! You'll get the full 7GB/s read speeds for lightning-fast performance. Ideal for video editing and large file transfers.\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.4\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    className: \"text-center mt-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative inline-block\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur-lg opacity-30 animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                    size: \"lg\",\n                                                    onClick: ()=>setShowChecker(true),\n                                                    className: \"relative bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg font-semibold shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105\",\n                                                    children: [\n                                                        \"Try It Yourself - It's Free\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"ml-2\",\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 515,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 mt-4\",\n                                            children: \"No signup required • Instant results\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 518,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 74,\n            columnNumber: 9\n        }, this) : // Compatibility Checker\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white/80 backdrop-blur-md shadow-lg border-b border-gray-200/50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 mb-4 lg:mb-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"text-white\",\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 531,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 530,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl lg:text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent\",\n                                                    children: \"Compatibility Checker\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg text-gray-600\",\n                                                    children: \"Get instant compatibility analysis for your tech purchases\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 533,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 529,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>{\n                                        setShowChecker(false);\n                                        setCompatibilityResult(null);\n                                        setSelectedDevice(null);\n                                        setSelectedAccessory(null);\n                                    },\n                                    className: \"border-gray-300 hover:bg-gray-50 px-6 py-3\",\n                                    children: \"← Back to Home\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 543,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 528,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 527,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 526,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: !compatibilityResult ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DeviceSelector__WEBPACK_IMPORTED_MODULE_2__.DeviceSelector, {\n                                    onDeviceSelect: setSelectedDevice,\n                                    selectedDevice: selectedDevice\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 564,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 563,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AccessorySelector__WEBPACK_IMPORTED_MODULE_3__.AccessorySelector, {\n                                    onAccessorySelect: setSelectedAccessory,\n                                    selectedAccessory: selectedAccessory\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 571,\n                                columnNumber: 17\n                            }, this),\n                            selectedDevice && selectedAccessory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: checkCompatibility,\n                                    loading: loading,\n                                    size: \"lg\",\n                                    className: \"px-8 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"mr-2\",\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 587,\n                                            columnNumber: 23\n                                        }, this),\n                                        \"Check Compatibility\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 581,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 19\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 561,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: resetSelection,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    children: \"← Check Another Combination\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 596,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 595,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CompatibilityResult__WEBPACK_IMPORTED_MODULE_4__.CompatibilityResult, {\n                                data: compatibilityResult\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 604,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 594,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 559,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 525,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"lRPJOyAYcgr3wdpYHy6KWuM0LnE=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});