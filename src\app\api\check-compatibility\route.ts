import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/database/connection';
import Device from '@/lib/database/models/Device';
import Accessory from '@/lib/database/models/Accessory';

export async function POST(request: NextRequest) {
  try {
    const { deviceId, accessoryId } = await request.json();

    if (!deviceId || !accessoryId) {
      return NextResponse.json(
        { error: 'Device ID and Accessory ID are required' },
        { status: 400 }
      );
    }

    let device, accessory;

    try {
      await connectDB();
      device = await Device.findById(deviceId);
      accessory = await Accessory.findById(accessoryId);
    } catch (dbError) {
      // Fallback to sample data if database is not available
      device = getSampleDevice(deviceId);
      accessory = getSampleAccessory(accessoryId);
    }

    if (!device || !accessory) {
      return NextResponse.json(
        { error: 'Device or accessory not found' },
        { status: 404 }
      );
    }

    const compatibilityResult = checkCompatibility(device, accessory);

    // Get suggestions for better alternatives if compatibility is low
    let suggestions = [];
    if (compatibilityResult.score < 80) {
      suggestions = getSampleSuggestions(accessory.type);
    }

    return NextResponse.json({
      device: {
        name: device.name,
        brand: device.brand,
        type: device.type
      },
      accessory: {
        name: accessory.name,
        brand: accessory.brand,
        type: accessory.type,
        price: accessory.price
      },
      compatibility: compatibilityResult,
      suggestions
    });
  } catch (error) {
    console.error('Error checking compatibility:', error);
    return NextResponse.json(
      { error: 'Failed to check compatibility' },
      { status: 500 }
    );
  }
}

function getSampleDevice(deviceId: string) {
  const devices = [
    {
      _id: '1',
      name: 'iPhone 15 Pro',
      brand: 'Apple',
      type: 'smartphone',
      specs: {
        bluetooth: '5.3',
        supportedCodecs: ['AAC', 'SBC']
      }
    },
    {
      _id: '2',
      name: 'Galaxy S24 Ultra',
      brand: 'Samsung',
      type: 'smartphone',
      specs: {
        bluetooth: '5.3',
        supportedCodecs: ['AAC', 'SBC', 'LDAC', 'Samsung Scalable']
      }
    },
    {
      _id: '3',
      name: 'MacBook Pro M3',
      brand: 'Apple',
      type: 'laptop',
      specs: {
        bluetooth: '5.3',
        supportedCodecs: ['AAC', 'SBC']
      }
    },
    {
      _id: '4',
      name: 'ThinkPad X1 Carbon',
      brand: 'Lenovo',
      type: 'laptop',
      specs: {
        bluetooth: '5.1',
        supportedCodecs: ['AAC', 'SBC']
      }
    },
    {
      _id: '5',
      name: 'iPad Pro',
      brand: 'Apple',
      type: 'tablet',
      specs: {
        bluetooth: '5.3',
        supportedCodecs: ['AAC', 'SBC']
      }
    }
  ];
  return devices.find(d => d._id === deviceId);
}

function getSampleAccessory(accessoryId: string) {
  const accessories = [
    {
      _id: '1',
      name: 'AirPods Pro 2',
      brand: 'Apple',
      type: 'tws',
      specs: {
        supportedCodecs: ['AAC', 'SBC'],
        features: ['ANC', 'Spatial Audio']
      },
      price: { amount: 249, currency: 'USD' }
    },
    {
      _id: '2',
      name: 'WF-1000XM4',
      brand: 'Sony',
      type: 'tws',
      specs: {
        supportedCodecs: ['AAC', 'SBC', 'LDAC'],
        features: ['ANC', 'Touch Control']
      },
      price: { amount: 199, currency: 'USD' }
    },
    {
      _id: '3',
      name: '980 PRO',
      brand: 'Samsung',
      type: 'ssd',
      specs: {
        storageInterface: 'NVMe',
        capacity: '1TB'
      },
      price: { amount: 129, currency: 'USD' }
    },
    {
      _id: '4',
      name: 'USB-C Charger 65W',
      brand: 'Anker',
      type: 'charger',
      specs: {
        powerOutput: '65W',
        cableType: 'Type-C'
      },
      price: { amount: 39, currency: 'USD' }
    },
    {
      _id: '5',
      name: 'Studio Display',
      brand: 'Apple',
      type: 'monitor',
      specs: {
        resolution: '5K',
        refreshRate: [60]
      },
      price: { amount: 1599, currency: 'USD' }
    }
  ];
  return accessories.find(a => a._id === accessoryId);
}

function getSampleSuggestions(type: string) {
  return [
    {
      _id: 'alt1',
      name: 'Alternative Product 1',
      brand: 'Brand',
      price: { amount: 150, currency: 'USD' }
    }
  ];
}

function checkCompatibility(device: any, accessory: any) {
  // Simple compatibility logic
  let score = 50;
  const compatibleFeatures = [];
  const incompatibleFeatures = [];
  const warnings = [];
  const suggestions = [];

  if (accessory.type === 'tws') {
    // Check Bluetooth compatibility
    if (device.specs.bluetooth && parseFloat(device.specs.bluetooth) >= 5.0) {
      compatibleFeatures.push('Bluetooth 5.0+ supported');
      score += 20;
    }

    // Check codec compatibility
    const deviceCodecs = device.specs.supportedCodecs || [];
    const accessoryCodecs = accessory.specs.supportedCodecs || [];
    const commonCodecs = deviceCodecs.filter(codec => accessoryCodecs.includes(codec));

    if (commonCodecs.length > 0) {
      compatibleFeatures.push(`Audio codecs: ${commonCodecs.join(', ')}`);
      score += 20;
    }

    const missingCodecs = accessoryCodecs.filter(codec => !deviceCodecs.includes(codec));
    if (missingCodecs.length > 0) {
      incompatibleFeatures.push(`Unsupported codecs: ${missingCodecs.join(', ')}`);
      warnings.push('Some audio features may not work optimally');
    }
  }

  return {
    compatible: score >= 60,
    compatibleFeatures,
    incompatibleFeatures,
    warnings,
    suggestions,
    score: Math.min(score, 100)
  };
}
    
    return NextResponse.json({
      device: {
        name: device.name,
        brand: device.brand,
        type: device.type
      },
      accessory: {
        name: accessory.name,
        brand: accessory.brand,
        type: accessory.type,
        price: accessory.price
      },
      compatibility: compatibilityResult,
      suggestions
    });
    
  } catch (error) {
    console.error('Error checking compatibility:', error);
    return NextResponse.json(
      { error: 'Failed to check compatibility' },
      { status: 500 }
    );
  }
}
