import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/database/connection';
import Device from '@/lib/database/models/Device';
import Accessory from '@/lib/database/models/Accessory';
import { CompatibilityEngine } from '@/lib/compatibility/engine';

export async function POST(request: NextRequest) {
  try {
    await connectDB();
    
    const { deviceId, accessoryId } = await request.json();
    
    if (!deviceId || !accessoryId) {
      return NextResponse.json(
        { error: 'Device ID and Accessory ID are required' },
        { status: 400 }
      );
    }
    
    const [device, accessory] = await Promise.all([
      Device.findById(deviceId),
      Accessory.findById(accessoryId)
    ]);
    
    if (!device) {
      return NextResponse.json(
        { error: 'Device not found' },
        { status: 404 }
      );
    }
    
    if (!accessory) {
      return NextResponse.json(
        { error: 'Accessory not found' },
        { status: 404 }
      );
    }
    
    const compatibilityResult = CompatibilityEngine.checkCompatibility(device, accessory);
    
    // Find similar accessories if compatibility is low
    let suggestions = [];
    if (compatibilityResult.score < 70) {
      suggestions = await Accessory.find({
        type: accessory.type,
        _id: { $ne: accessoryId },
        'compatibility.deviceTypes': device.type
      })
      .select('name brand price imageUrl')
      .limit(3);
    }
    
    return NextResponse.json({
      device: {
        name: device.name,
        brand: device.brand,
        type: device.type
      },
      accessory: {
        name: accessory.name,
        brand: accessory.brand,
        type: accessory.type,
        price: accessory.price
      },
      compatibility: compatibilityResult,
      suggestions
    });
    
  } catch (error) {
    console.error('Error checking compatibility:', error);
    return NextResponse.json(
      { error: 'Failed to check compatibility' },
      { status: 500 }
    );
  }
}
