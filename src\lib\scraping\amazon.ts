import axios from 'axios';
import * as cheerio from 'cheerio';

export interface AmazonProduct {
  name: string;
  brand: string;
  price?: {
    amount: number;
    currency: string;
  };
  features: string[];
  specifications: Record<string, string>;
  rating?: number;
  reviewCount?: number;
}

export class AmazonScraper {
  private static readonly BASE_URL = 'https://amazon.com';
  private static readonly DELAY_MS = 3000; // Longer delay for Amazon

  static async searchProducts(query: string, category?: string): Promise<string[]> {
    try {
      await this.delay(this.DELAY_MS);
      
      let searchUrl = `${this.BASE_URL}/s?k=${encodeURIComponent(query)}`;
      if (category) {
        searchUrl += `&i=${category}`;
      }

      const response = await axios.get(searchUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept-Language': 'en-US,en;q=0.9',
          'Accept-Encoding': 'gzip, deflate, br',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
        }
      });

      const $ = cheerio.load(response.data);
      const productLinks: string[] = [];

      $('[data-component-type="s-search-result"] h2 a').each((_, element) => {
        const href = $(element).attr('href');
        if (href) {
          productLinks.push(`${this.BASE_URL}${href}`);
        }
      });

      return productLinks.slice(0, 10);
    } catch (error) {
      console.error('Error searching Amazon:', error);
      return [];
    }
  }

  static async scrapeProduct(url: string): Promise<AmazonProduct | null> {
    try {
      await this.delay(this.DELAY_MS);
      
      const response = await axios.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept-Language': 'en-US,en;q=0.9',
          'Accept-Encoding': 'gzip, deflate, br',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
        }
      });

      const $ = cheerio.load(response.data);
      
      // Extract product name
      const name = $('#productTitle').text().trim();
      
      // Extract brand (try multiple selectors)
      let brand = '';
      brand = brand || $('tr:contains("Brand") td').last().text().trim();
      brand = brand || $('span:contains("Brand:") + span').text().trim();
      brand = brand || $('[data-feature-name="bylineInfo"] a').text().trim();
      
      if (!brand && name) {
        // Try to extract brand from product name
        const brandMatch = name.match(/^([A-Za-z]+)/);
        brand = brandMatch ? brandMatch[1] : '';
      }

      // Extract price
      let price: { amount: number; currency: string } | undefined;
      const priceText = $('.a-price-whole').first().text() + '.' + $('.a-price-fraction').first().text();
      const priceMatch = priceText.match(/(\d+\.?\d*)/);
      if (priceMatch) {
        price = {
          amount: parseFloat(priceMatch[1]),
          currency: 'USD'
        };
      }

      // Extract features
      const features: string[] = [];
      $('#feature-bullets ul li span').each((_, element) => {
        const feature = $(element).text().trim();
        if (feature && !feature.includes('Make sure') && feature.length > 10) {
          features.push(feature);
        }
      });

      // Extract specifications
      const specifications: Record<string, string> = {};
      $('#productDetails_techSpec_section_1 tr, #productDetails_detailBullets_sections1 tr').each((_, row) => {
        const cells = $(row).find('td');
        if (cells.length >= 2) {
          const key = $(cells[0]).text().trim().replace(':', '');
          const value = $(cells[1]).text().trim();
          if (key && value) {
            specifications[key] = value;
          }
        }
      });

      // Extract rating
      let rating: number | undefined;
      const ratingText = $('[data-hook="average-star-rating"] span').text();
      const ratingMatch = ratingText.match(/(\d+\.?\d*)/);
      if (ratingMatch) {
        rating = parseFloat(ratingMatch[1]);
      }

      // Extract review count
      let reviewCount: number | undefined;
      const reviewText = $('[data-hook="total-review-count"]').text();
      const reviewMatch = reviewText.match(/(\d+)/);
      if (reviewMatch) {
        reviewCount = parseInt(reviewMatch[1]);
      }

      return {
        name,
        brand,
        price,
        features,
        specifications,
        rating,
        reviewCount
      };
    } catch (error) {
      console.error('Error scraping product from Amazon:', error);
      return null;
    }
  }

  static extractTWSSpecs(product: AmazonProduct): {
    supportedCodecs: string[];
    features: string[];
    batteryLife?: string;
    chargingMethod?: string[];
  } {
    const supportedCodecs: string[] = [];
    const features: string[] = [];
    let batteryLife: string | undefined;
    const chargingMethod: string[] = [];

    // Combine all text for analysis
    const allText = [
      product.name,
      ...product.features,
      ...Object.values(product.specifications)
    ].join(' ').toLowerCase();

    // Extract codecs
    if (allText.includes('ldac')) supportedCodecs.push('LDAC');
    if (allText.includes('aptx')) supportedCodecs.push('aptX');
    if (allText.includes('aac')) supportedCodecs.push('AAC');
    if (allText.includes('sbc')) supportedCodecs.push('SBC');

    // Extract features
    if (allText.includes('anc') || allText.includes('noise cancel')) features.push('ANC');
    if (allText.includes('transparency') || allText.includes('ambient')) features.push('Transparency Mode');
    if (allText.includes('touch control')) features.push('Touch Control');
    if (allText.includes('voice assistant')) features.push('Voice Assistant');
    if (allText.includes('waterproof') || allText.includes('ipx')) features.push('Water Resistant');

    // Extract battery life
    const batteryMatch = allText.match(/(\d+)\s*hours?\s*(battery|playback)/);
    if (batteryMatch) {
      batteryLife = `${batteryMatch[1]} hours`;
    }

    // Extract charging method
    if (allText.includes('wireless charging')) chargingMethod.push('Wireless');
    if (allText.includes('usb-c') || allText.includes('type-c')) chargingMethod.push('USB-C');
    if (allText.includes('lightning')) chargingMethod.push('Lightning');

    return {
      supportedCodecs: supportedCodecs.length > 0 ? supportedCodecs : ['AAC', 'SBC'],
      features,
      batteryLife,
      chargingMethod
    };
  }

  private static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
