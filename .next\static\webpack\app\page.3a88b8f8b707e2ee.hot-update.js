"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_NoSSR__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/NoSSR */ \"(app-pages-browser)/./src/components/NoSSR.tsx\");\n/* harmony import */ var _components_DeviceSelector__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/DeviceSelector */ \"(app-pages-browser)/./src/components/DeviceSelector.tsx\");\n/* harmony import */ var _components_AccessorySelector__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/AccessorySelector */ \"(app-pages-browser)/./src/components/AccessorySelector.tsx\");\n/* harmony import */ var _components_CompatibilityResult__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/CompatibilityResult */ \"(app-pages-browser)/./src/components/CompatibilityResult.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/Badge */ \"(app-pages-browser)/./src/components/ui/Badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/headphones.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,CheckCircle,HardDrive,Headphones,Search,Smartphone,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [selectedDevice, setSelectedDevice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedAccessory, setSelectedAccessory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [compatibilityResult, setCompatibilityResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showChecker, setShowChecker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const checkCompatibility = async ()=>{\n        if (!selectedDevice || !selectedAccessory) return;\n        setLoading(true);\n        try {\n            const response = await fetch('/api/check-compatibility', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    deviceId: selectedDevice._id,\n                    accessoryId: selectedAccessory._id\n                })\n            });\n            const data = await response.json();\n            setCompatibilityResult(data);\n        } catch (error) {\n            console.error('Error checking compatibility:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const resetSelection = ()=>{\n        setSelectedDevice(null);\n        setSelectedAccessory(null);\n        setCompatibilityResult(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NoSSR__WEBPACK_IMPORTED_MODULE_2__.NoSSR, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\",\n            children: !showChecker ? // Landing Page\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"relative overflow-hidden bg-gradient-to-br from-indigo-900 via-blue-900 to-purple-900\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 opacity-20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0\",\n                                    style: {\n                                        backgroundImage: \"radial-gradient(circle at 25px 25px, rgba(255,255,255,0.1) 2px, transparent 0)\",\n                                        backgroundSize: '50px 50px'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white/90 text-sm font-medium mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Save Money • Avoid Incompatible Purchases\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-5xl md:text-7xl font-extrabold mb-8 bg-gradient-to-r from-white via-blue-100 to-purple-200 bg-clip-text text-transparent leading-tight\",\n                                            children: [\n                                                \"Don't Waste Money on Features\",\n                                                ' ',\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent\",\n                                                    children: \"You Can't Use\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl md:text-2xl mb-12 text-blue-100 max-w-4xl mx-auto leading-relaxed\",\n                                            children: \"Get instant compatibility analysis for your tech purchases. Know exactly what features will work with your devices before you buy.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row gap-4 justify-center items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                    size: \"lg\",\n                                                    onClick: ()=>setShowChecker(true),\n                                                    className: \"bg-gradient-to-r from-yellow-400 to-orange-500 text-black hover:from-yellow-300 hover:to-orange-400 text-lg px-8 py-4 font-semibold shadow-2xl hover:shadow-yellow-500/25 transition-all duration-300 transform hover:scale-105\",\n                                                    children: [\n                                                        \"Check Compatibility Now\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"ml-2\",\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 113,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"lg\",\n                                                    className: \"border-white/30 text-white hover:bg-white/10 backdrop-blur-sm px-8 py-4\",\n                                                    onClick: ()=>{\n                                                        var _document_getElementById;\n                                                        (_document_getElementById = document.getElementById('how-it-works')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                                                            behavior: 'smooth'\n                                                        });\n                                                    },\n                                                    children: \"Learn How It Works\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-3 gap-8 mt-16 max-w-2xl mx-auto\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-3xl font-bold text-white mb-2\",\n                                                            children: \"1000+\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-blue-200 text-sm\",\n                                                            children: \"Devices Supported\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 132,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-3xl font-bold text-white mb-2\",\n                                                            children: \"95%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-blue-200 text-sm\",\n                                                            children: \"Accuracy Rate\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 136,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-3xl font-bold text-white mb-2\",\n                                                            children: \"$500+\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 139,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-blue-200 text-sm\",\n                                                            children: \"Average Savings\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-20 left-10 lg:left-20 animate-bounce\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: 32,\n                                                className: \"text-blue-300\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-32 right-10 lg:right-20 animate-pulse\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                size: 28,\n                                                className: \"text-purple-300\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-32 left-16 lg:left-32 animate-bounce\",\n                                        style: {\n                                            animationDelay: '1s'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                size: 30,\n                                                className: \"text-green-300\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-20 right-16 lg:right-32 animate-pulse\",\n                                        style: {\n                                            animationDelay: '2s'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                size: 26,\n                                                className: \"text-yellow-300\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"how-it-works\",\n                        className: \"py-24 bg-white relative overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 left-0 w-full h-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-20 left-10 w-72 h-72 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-40 right-10 w-72 h-72 bg-purple-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse\",\n                                        style: {\n                                            animationDelay: '2s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-20 left-1/2 w-72 h-72 bg-indigo-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse\",\n                                        style: {\n                                            animationDelay: '4s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 text-sm font-medium mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"w-2 h-2 bg-blue-500 rounded-full mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Simple Process\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent mb-6\",\n                                                children: \"How It Works\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\",\n                                                children: \"Get detailed compatibility analysis in just 3 simple steps. Our AI-powered system analyzes thousands of device specifications instantly.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid md:grid-cols-3 gap-8 lg:gap-12\",\n                                        children: [\n                                            {\n                                                step: \"01\",\n                                                title: \"Select Your Device\",\n                                                description: \"Choose your smartphone, laptop, or tablet from our comprehensive database of 1000+ devices\",\n                                                icon: _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                                gradient: \"from-blue-500 to-cyan-500\",\n                                                bgGradient: \"from-blue-50 to-cyan-50\"\n                                            },\n                                            {\n                                                step: \"02\",\n                                                title: \"Pick an Accessory\",\n                                                description: \"Select the accessory you want to check - TWS earbuds, SSD, charger, cable, monitor, or soundbar\",\n                                                icon: _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                                gradient: \"from-purple-500 to-pink-500\",\n                                                bgGradient: \"from-purple-50 to-pink-50\"\n                                            },\n                                            {\n                                                step: \"03\",\n                                                title: \"Get Smart Results\",\n                                                description: \"Receive detailed compatibility analysis with AI explanations, recommendations, and direct purchase links\",\n                                                icon: _barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                                gradient: \"from-green-500 to-emerald-500\",\n                                                bgGradient: \"from-green-50 to-emerald-50\"\n                                            }\n                                        ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 30\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.8,\n                                                    delay: index * 0.2\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                },\n                                                whileHover: {\n                                                    y: -8,\n                                                    transition: {\n                                                        duration: 0.3\n                                                    }\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative p-8 rounded-3xl bg-gradient-to-br \".concat(item.bgGradient, \" border border-white/50 shadow-xl hover:shadow-2xl transition-all duration-300 h-full group\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute -top-4 -left-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-12 h-12 rounded-2xl bg-gradient-to-r \".concat(item.gradient, \" flex items-center justify-center text-white font-bold text-lg shadow-lg\"),\n                                                                children: item.step\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 235,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-20 h-20 rounded-2xl bg-gradient-to-r \".concat(item.gradient, \" flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                                size: 36,\n                                                                className: \"text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 242,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                                                    children: item.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 247,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 leading-relaxed\",\n                                                                    children: item.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 248,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-4 right-4 w-8 h-8 bg-white/20 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute bottom-4 left-4 w-6 h-6 bg-white/10 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-24 bg-gradient-to-br from-gray-50 to-blue-50 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 opacity-30\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0\",\n                                    style: {\n                                        backgroundImage: \"radial-gradient(circle at 20px 20px, rgba(0,0,0,0.02) 10px, transparent 0)\",\n                                        backgroundSize: '40px 40px'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        className: \"text-center mb-20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-indigo-100 to-blue-100 text-indigo-800 text-sm font-medium mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"w-2 h-2 bg-indigo-500 rounded-full mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Real Examples\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent mb-6\",\n                                                children: \"See It In Action\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\",\n                                                children: \"Real compatibility scenarios showing how our tool prevents costly mistakes and helps you make informed decisions.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid lg:grid-cols-2 gap-8 lg:gap-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: -30\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.8\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                },\n                                                whileHover: {\n                                                    y: -5,\n                                                    transition: {\n                                                        duration: 0.3\n                                                    }\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative p-8 bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-300 border border-gray-100 overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full -translate-y-16 translate-x-16 opacity-50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative flex items-center justify-center space-x-6 mb-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                className: \"text-white\",\n                                                                                size: 28\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 307,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 306,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute -bottom-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"text-white\",\n                                                                                size: 14\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 310,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 309,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 305,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-3xl font-bold text-gray-300\",\n                                                                    children: \"+\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 314,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                className: \"text-white\",\n                                                                                size: 28\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 318,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 317,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute -bottom-2 -right-2 w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"text-white\",\n                                                                                size: 14\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 321,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 320,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 316,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center mb-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-bold text-gray-900 mb-2\",\n                                                                    children: \"iPhone 15 Pro + Sony WF-1000XM4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-flex items-center px-3 py-1 rounded-full bg-yellow-100 text-yellow-800 text-sm font-medium\",\n                                                                    children: \"75% Compatible\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 328,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3 mb-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between p-3 bg-green-50 rounded-xl\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                    className: \"text-green-600 mr-2\",\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 336,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium text-green-800\",\n                                                                                    children: \"AAC Codec\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 337,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 335,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                            variant: \"success\",\n                                                                            className: \"text-xs\",\n                                                                            children: \"Works\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 339,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between p-3 bg-red-50 rounded-xl\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                    className: \"text-red-600 mr-2\",\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 344,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium text-red-800\",\n                                                                                    children: \"LDAC Support\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 345,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 343,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                            variant: \"error\",\n                                                                            className: \"text-xs\",\n                                                                            children: \"Missing\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 347,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 342,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between p-3 bg-yellow-50 rounded-xl\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                    className: \"text-yellow-600 mr-2\",\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 352,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium text-yellow-800\",\n                                                                                    children: \"Hi-Res Audio\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 353,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 351,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                            variant: \"warning\",\n                                                                            className: \"text-xs\",\n                                                                            children: \"Limited\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 355,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 350,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-gray-50 rounded-xl\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 leading-relaxed\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        className: \"text-gray-900\",\n                                                                        children: \"Result:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 361,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" Great sound quality with AAC, but you won't get LDAC's hi-res audio benefits. Consider alternatives for audiophile experience.\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: 30\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.8,\n                                                    delay: 0.2\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                },\n                                                whileHover: {\n                                                    y: -5,\n                                                    transition: {\n                                                        duration: 0.3\n                                                    }\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative p-8 bg-white rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-300 border border-gray-100 overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-0 left-0 w-32 h-32 bg-gradient-to-br from-green-100 to-emerald-100 rounded-full -translate-y-16 -translate-x-16 opacity-50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative flex items-center justify-center space-x-6 mb-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-16 h-16 bg-gradient-to-r from-gray-600 to-gray-700 rounded-2xl flex items-center justify-center shadow-lg\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                className: \"w-8 h-8 text-white\",\n                                                                                fill: \"currentColor\",\n                                                                                viewBox: \"0 0 24 24\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    d: \"M20 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4V8h16v10z\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 383,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 382,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 381,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute -bottom-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"text-white\",\n                                                                                size: 14\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 387,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 386,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 380,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-3xl font-bold text-gray-300\",\n                                                                    children: \"+\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                className: \"text-white\",\n                                                                                size: 28\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 395,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 394,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute -bottom-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"text-white\",\n                                                                                size: 14\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 398,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 397,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 393,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center mb-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-bold text-gray-900 mb-2\",\n                                                                    children: \"MacBook Pro M3 + Samsung 980 PRO\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 404,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"inline-flex items-center px-3 py-1 rounded-full bg-green-100 text-green-800 text-sm font-medium\",\n                                                                    children: \"100% Compatible\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 405,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3 mb-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between p-3 bg-green-50 rounded-xl\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                    className: \"text-green-600 mr-2\",\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 413,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium text-green-800\",\n                                                                                    children: \"NVMe Interface\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 414,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 412,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                            variant: \"success\",\n                                                                            className: \"text-xs\",\n                                                                            children: \"Perfect\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 416,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 411,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between p-3 bg-green-50 rounded-xl\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                    className: \"text-green-600 mr-2\",\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 421,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium text-green-800\",\n                                                                                    children: \"PCIe 4.0 Support\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 422,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 420,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                            variant: \"success\",\n                                                                            className: \"text-xs\",\n                                                                            children: \"Supported\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 424,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 419,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between p-3 bg-green-50 rounded-xl\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                    className: \"text-green-600 mr-2\",\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 429,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium text-green-800\",\n                                                                                    children: \"Full Speed (7GB/s)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 430,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 428,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                            variant: \"success\",\n                                                                            className: \"text-xs\",\n                                                                            children: \"Maximum\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 432,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 427,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 410,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-green-50 rounded-xl\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600 leading-relaxed\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        className: \"text-gray-900\",\n                                                                        children: \"Result:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 438,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" Perfect match! You'll get the full 7GB/s read speeds for lightning-fast performance. Ideal for video editing and large file transfers.\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 30\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.4\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        className: \"text-center mt-16\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative inline-block\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl blur-lg opacity-30 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                        size: \"lg\",\n                                                        onClick: ()=>setShowChecker(true),\n                                                        className: \"relative bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg font-semibold shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105\",\n                                                        children: [\n                                                            \"Try It Yourself - It's Free\",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"ml-2\",\n                                                                size: 20\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500 mt-4\",\n                                                children: \"No signup required • Instant results\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 76,\n                columnNumber: 9\n            }, this) : // Compatibility Checker\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/80 backdrop-blur-md shadow-lg border-b border-gray-200/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-4 lg:mb-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"text-white\",\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-3xl lg:text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent\",\n                                                        children: \"Compatibility Checker\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg text-gray-600\",\n                                                        children: \"Get instant compatibility analysis for your tech purchases\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>{\n                                            setShowChecker(false);\n                                            setCompatibilityResult(null);\n                                            setSelectedDevice(null);\n                                            setSelectedAccessory(null);\n                                        },\n                                        className: \"border-gray-300 hover:bg-gray-50 px-6 py-3\",\n                                        children: \"← Back to Home\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 472,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 471,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                        children: !compatibilityResult ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid lg:grid-cols-2 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-lg p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DeviceSelector__WEBPACK_IMPORTED_MODULE_3__.DeviceSelector, {\n                                        onDeviceSelect: setSelectedDevice,\n                                        selectedDevice: selectedDevice\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 509,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-lg p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AccessorySelector__WEBPACK_IMPORTED_MODULE_4__.AccessorySelector, {\n                                        onAccessorySelect: setSelectedAccessory,\n                                        selectedAccessory: selectedAccessory\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 516,\n                                    columnNumber: 17\n                                }, this),\n                                selectedDevice && selectedAccessory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: checkCompatibility,\n                                        loading: loading,\n                                        size: \"lg\",\n                                        className: \"px-8 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_CheckCircle_HardDrive_Headphones_Search_Smartphone_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"mr-2\",\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 532,\n                                                columnNumber: 23\n                                            }, this),\n                                            \"Check Compatibility\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 525,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 506,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: resetSelection,\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        children: \"← Check Another Combination\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CompatibilityResult__WEBPACK_IMPORTED_MODULE_5__.CompatibilityResult, {\n                                    data: compatibilityResult\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 549,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 539,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 504,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 470,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 73,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\WebProject\\\\productfinder\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"lRPJOyAYcgr3wdpYHy6KWuM0LnE=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRW1EO0FBQ1I7QUFDa0I7QUFDTTtBQUNJO0FBQ3ZCO0FBRUY7QUFDaUY7QUFzQmhILFNBQVNpQjs7SUFDdEIsTUFBTSxDQUFDQyxnQkFBZ0JDLGtCQUFrQixHQUFHbEIsK0NBQVFBLENBQWdCO0lBQ3BFLE1BQU0sQ0FBQ21CLG1CQUFtQkMscUJBQXFCLEdBQUdwQiwrQ0FBUUEsQ0FBbUI7SUFDN0UsTUFBTSxDQUFDcUIscUJBQXFCQyx1QkFBdUIsR0FBR3RCLCtDQUFRQSxDQUFNO0lBQ3BFLE1BQU0sQ0FBQ3VCLFNBQVNDLFdBQVcsR0FBR3hCLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ3lCLGFBQWFDLGVBQWUsR0FBRzFCLCtDQUFRQSxDQUFDO0lBRS9DLE1BQU0yQixxQkFBcUI7UUFDekIsSUFBSSxDQUFDVixrQkFBa0IsQ0FBQ0UsbUJBQW1CO1FBRTNDSyxXQUFXO1FBQ1gsSUFBSTtZQUNGLE1BQU1JLFdBQVcsTUFBTUMsTUFBTSw0QkFBNEI7Z0JBQ3ZEQyxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFDbkJDLFVBQVVsQixlQUFlbUIsR0FBRztvQkFDNUJDLGFBQWFsQixrQkFBa0JpQixHQUFHO2dCQUNwQztZQUNGO1lBRUEsTUFBTUUsT0FBTyxNQUFNVixTQUFTVyxJQUFJO1lBQ2hDakIsdUJBQXVCZ0I7UUFDekIsRUFBRSxPQUFPRSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxpQ0FBaUNBO1FBQ2pELFNBQVU7WUFDUmhCLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTWtCLGlCQUFpQjtRQUNyQnhCLGtCQUFrQjtRQUNsQkUscUJBQXFCO1FBQ3JCRSx1QkFBdUI7SUFDekI7SUFFQSxxQkFDRSw4REFBQ3JCLG9EQUFLQTtrQkFDSiw0RUFBQzBDO1lBQUlDLFdBQVU7c0JBQ1osQ0FBQ25CLGNBQ0YsZUFBZTswQkFDZiw4REFBQ2tCO2dCQUFJQyxXQUFVOztrQ0FFYiw4REFBQ0M7d0JBQVFELFdBQVU7OzBDQUVqQiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNEO29DQUFJQyxXQUFVO29DQUFtQkUsT0FBTzt3Q0FDdkNDLGlCQUFrQjt3Q0FDbEJDLGdCQUFnQjtvQ0FDbEI7Ozs7Ozs7Ozs7OzBDQUdGLDhEQUFDTDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUViLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNLO29EQUFLTCxXQUFVOzs7Ozs7Z0RBQThEOzs7Ozs7O3NEQUloRiw4REFBQ007NENBQUdOLFdBQVU7O2dEQUE4STtnREFDNUg7OERBQzlCLDhEQUFDSztvREFBS0wsV0FBVTs4REFBK0U7Ozs7Ozs7Ozs7OztzREFLakcsOERBQUNPOzRDQUFFUCxXQUFVO3NEQUE0RTs7Ozs7O3NEQUl6Riw4REFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDdkMseURBQU1BO29EQUNMK0MsTUFBSztvREFDTEMsU0FBUyxJQUFNM0IsZUFBZTtvREFDOUJrQixXQUFVOzt3REFDWDtzRUFFQyw4REFBQzdCLG1LQUFVQTs0REFBQzZCLFdBQVU7NERBQU9RLE1BQU07Ozs7Ozs7Ozs7Ozs4REFHckMsOERBQUMvQyx5REFBTUE7b0RBQ0xpRCxTQUFRO29EQUNSRixNQUFLO29EQUNMUixXQUFVO29EQUNWUyxTQUFTOzREQUNQRTt5REFBQUEsMkJBQUFBLFNBQVNDLGNBQWMsQ0FBQyw2QkFBeEJELCtDQUFBQSx5QkFBeUNFLGNBQWMsQ0FBQzs0REFBRUMsVUFBVTt3REFBUztvREFDL0U7OERBQ0Q7Ozs7Ozs7Ozs7OztzREFNSCw4REFBQ2Y7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVO3NFQUFxQzs7Ozs7O3NFQUNwRCw4REFBQ0Q7NERBQUlDLFdBQVU7c0VBQXdCOzs7Ozs7Ozs7Ozs7OERBRXpDLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVO3NFQUFxQzs7Ozs7O3NFQUNwRCw4REFBQ0Q7NERBQUlDLFdBQVU7c0VBQXdCOzs7Ozs7Ozs7Ozs7OERBRXpDLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVO3NFQUFxQzs7Ozs7O3NFQUNwRCw4REFBQ0Q7NERBQUlDLFdBQVU7c0VBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FPL0MsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNEOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDcEMsbUtBQVVBO2dEQUFDNEMsTUFBTTtnREFBSVIsV0FBVTs7Ozs7Ozs7Ozs7Ozs7OztrREFJcEMsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ25DLG9LQUFVQTtnREFBQzJDLE1BQU07Z0RBQUlSLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSXBDLDhEQUFDRDt3Q0FBSUMsV0FBVTt3Q0FBdURFLE9BQU87NENBQUVhLGdCQUFnQjt3Q0FBSztrREFDbEcsNEVBQUNoQjs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ2xDLG9LQUFTQTtnREFBQzBDLE1BQU07Z0RBQUlSLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSW5DLDhEQUFDRDt3Q0FBSUMsV0FBVTt3Q0FBd0RFLE9BQU87NENBQUVhLGdCQUFnQjt3Q0FBSztrREFDbkcsNEVBQUNoQjs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ2pDLG9LQUFHQTtnREFBQ3lDLE1BQU07Z0RBQUlSLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBT2pDLDhEQUFDQzt3QkFBUWUsSUFBRzt3QkFBZWhCLFdBQVU7OzBDQUVuQyw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7Ozs7O2tEQUNmLDhEQUFDRDt3Q0FBSUMsV0FBVTt3Q0FBMkhFLE9BQU87NENBQUVhLGdCQUFnQjt3Q0FBSzs7Ozs7O2tEQUN4Syw4REFBQ2hCO3dDQUFJQyxXQUFVO3dDQUE4SEUsT0FBTzs0Q0FBRWEsZ0JBQWdCO3dDQUFLOzs7Ozs7Ozs7Ozs7MENBRzdLLDhEQUFDaEI7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0s7d0RBQUtMLFdBQVU7Ozs7OztvREFBK0M7Ozs7Ozs7MERBR2pFLDhEQUFDaUI7Z0RBQUdqQixXQUFVOzBEQUErRzs7Ozs7OzBEQUc3SCw4REFBQ087Z0RBQUVQLFdBQVU7MERBQTBEOzs7Ozs7Ozs7Ozs7a0RBS3pFLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDWjs0Q0FDQztnREFDRWtCLE1BQU07Z0RBQ05DLE9BQU87Z0RBQ1BDLGFBQWE7Z0RBQ2JDLE1BQU16RCxtS0FBVUE7Z0RBQ2hCMEQsVUFBVTtnREFDVkMsWUFBWTs0Q0FDZDs0Q0FDQTtnREFDRUwsTUFBTTtnREFDTkMsT0FBTztnREFDUEMsYUFBYTtnREFDYkMsTUFBTXhELG9LQUFVQTtnREFDaEJ5RCxVQUFVO2dEQUNWQyxZQUFZOzRDQUNkOzRDQUNBO2dEQUNFTCxNQUFNO2dEQUNOQyxPQUFPO2dEQUNQQyxhQUFhO2dEQUNiQyxNQUFNckQsb0tBQVdBO2dEQUNqQnNELFVBQVU7Z0RBQ1ZDLFlBQVk7NENBQ2Q7eUNBQ0QsQ0FBQ0MsR0FBRyxDQUFDLENBQUNDLE1BQU1DLHNCQUNYLDhEQUFDQyxPQUFPNUIsR0FBRztnREFFVDZCLFNBQVM7b0RBQUVDLFNBQVM7b0RBQUdDLEdBQUc7Z0RBQUc7Z0RBQzdCQyxhQUFhO29EQUFFRixTQUFTO29EQUFHQyxHQUFHO2dEQUFFO2dEQUNoQ0UsWUFBWTtvREFBRUMsVUFBVTtvREFBS0MsT0FBT1IsUUFBUTtnREFBSTtnREFDaERTLFVBQVU7b0RBQUVDLE1BQU07Z0RBQUs7Z0RBQ3ZCQyxZQUFZO29EQUFFUCxHQUFHLENBQUM7b0RBQUdFLFlBQVk7d0RBQUVDLFVBQVU7b0RBQUk7Z0RBQUU7MERBRW5ELDRFQUFDbEM7b0RBQUlDLFdBQVcsOENBQThELE9BQWhCeUIsS0FBS0YsVUFBVSxFQUFDOztzRUFFNUUsOERBQUN4Qjs0REFBSUMsV0FBVTtzRUFDYiw0RUFBQ0Q7Z0VBQUlDLFdBQVcsMENBQXdELE9BQWR5QixLQUFLSCxRQUFRLEVBQUM7MEVBQ3JFRyxLQUFLUCxJQUFJOzs7Ozs7Ozs7OztzRUFLZCw4REFBQ25COzREQUFJQyxXQUFXLDBDQUF3RCxPQUFkeUIsS0FBS0gsUUFBUSxFQUFDO3NFQUN0RSw0RUFBQ0csS0FBS0osSUFBSTtnRUFBQ2IsTUFBTTtnRUFBSVIsV0FBVTs7Ozs7Ozs7Ozs7c0VBSWpDLDhEQUFDRDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNzQztvRUFBR3RDLFdBQVU7OEVBQXlDeUIsS0FBS04sS0FBSzs7Ozs7OzhFQUNqRSw4REFBQ1o7b0VBQUVQLFdBQVU7OEVBQWlDeUIsS0FBS0wsV0FBVzs7Ozs7Ozs7Ozs7O3NFQUloRSw4REFBQ3JCOzREQUFJQyxXQUFVOzs7Ozs7c0VBQ2YsOERBQUNEOzREQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7K0NBNUJaMEI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBcUNmLDhEQUFDekI7d0JBQVFELFdBQVU7OzBDQUVqQiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNEO29DQUFJQyxXQUFVO29DQUFtQkUsT0FBTzt3Q0FDdkNDLGlCQUFrQjt3Q0FDbEJDLGdCQUFnQjtvQ0FDbEI7Ozs7Ozs7Ozs7OzBDQUdGLDhEQUFDTDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUMyQixPQUFPNUIsR0FBRzt3Q0FDVDZCLFNBQVM7NENBQUVDLFNBQVM7NENBQUdDLEdBQUc7d0NBQUc7d0NBQzdCQyxhQUFhOzRDQUFFRixTQUFTOzRDQUFHQyxHQUFHO3dDQUFFO3dDQUNoQ0UsWUFBWTs0Q0FBRUMsVUFBVTt3Q0FBSTt3Q0FDNUJFLFVBQVU7NENBQUVDLE1BQU07d0NBQUs7d0NBQ3ZCcEMsV0FBVTs7MERBRVYsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0s7d0RBQUtMLFdBQVU7Ozs7OztvREFBaUQ7Ozs7Ozs7MERBR25FLDhEQUFDaUI7Z0RBQUdqQixXQUFVOzBEQUErRzs7Ozs7OzBEQUc3SCw4REFBQ087Z0RBQUVQLFdBQVU7MERBQTBEOzs7Ozs7Ozs7Ozs7a0RBS3pFLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUMyQixPQUFPNUIsR0FBRztnREFDVDZCLFNBQVM7b0RBQUVDLFNBQVM7b0RBQUdVLEdBQUcsQ0FBQztnREFBRztnREFDOUJSLGFBQWE7b0RBQUVGLFNBQVM7b0RBQUdVLEdBQUc7Z0RBQUU7Z0RBQ2hDUCxZQUFZO29EQUFFQyxVQUFVO2dEQUFJO2dEQUM1QkUsVUFBVTtvREFBRUMsTUFBTTtnREFBSztnREFDdkJDLFlBQVk7b0RBQUVQLEdBQUcsQ0FBQztvREFBR0UsWUFBWTt3REFBRUMsVUFBVTtvREFBSTtnREFBRTswREFFbkQsNEVBQUNsQztvREFBSUMsV0FBVTs7c0VBRWIsOERBQUNEOzREQUFJQyxXQUFVOzs7Ozs7c0VBR2YsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0Q7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDRDs0RUFBSUMsV0FBVTtzRkFDYiw0RUFBQ3BDLG1LQUFVQTtnRkFBQ29DLFdBQVU7Z0ZBQWFRLE1BQU07Ozs7Ozs7Ozs7O3NGQUUzQyw4REFBQ1Q7NEVBQUlDLFdBQVU7c0ZBQ2IsNEVBQUNoQyxvS0FBV0E7Z0ZBQUNnQyxXQUFVO2dGQUFhUSxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs4RUFJOUMsOERBQUNUO29FQUFJQyxXQUFVOzhFQUFtQzs7Ozs7OzhFQUVsRCw4REFBQ0Q7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDRDs0RUFBSUMsV0FBVTtzRkFDYiw0RUFBQ25DLG9LQUFVQTtnRkFBQ21DLFdBQVU7Z0ZBQWFRLE1BQU07Ozs7Ozs7Ozs7O3NGQUUzQyw4REFBQ1Q7NEVBQUlDLFdBQVU7c0ZBQ2IsNEVBQUM5QixvS0FBYUE7Z0ZBQUM4QixXQUFVO2dGQUFhUSxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzRUFLbEQsOERBQUNUOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ3NDO29FQUFHdEMsV0FBVTs4RUFBdUM7Ozs7Ozs4RUFDckQsOERBQUNEO29FQUFJQyxXQUFVOzhFQUFvRzs7Ozs7Ozs7Ozs7O3NFQUtySCw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNEOzRFQUFJQyxXQUFVOzs4RkFDYiw4REFBQ2hDLG9LQUFXQTtvRkFBQ2dDLFdBQVU7b0ZBQXNCUSxNQUFNOzs7Ozs7OEZBQ25ELDhEQUFDSDtvRkFBS0wsV0FBVTs4RkFBcUM7Ozs7Ozs7Ozs7OztzRkFFdkQsOERBQUN0Qyx1REFBS0E7NEVBQUNnRCxTQUFROzRFQUFVVixXQUFVO3NGQUFVOzs7Ozs7Ozs7Ozs7OEVBRy9DLDhEQUFDRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNEOzRFQUFJQyxXQUFVOzs4RkFDYiw4REFBQy9CLG9LQUFPQTtvRkFBQytCLFdBQVU7b0ZBQW9CUSxNQUFNOzs7Ozs7OEZBQzdDLDhEQUFDSDtvRkFBS0wsV0FBVTs4RkFBbUM7Ozs7Ozs7Ozs7OztzRkFFckQsOERBQUN0Qyx1REFBS0E7NEVBQUNnRCxTQUFROzRFQUFRVixXQUFVO3NGQUFVOzs7Ozs7Ozs7Ozs7OEVBRzdDLDhEQUFDRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNEOzRFQUFJQyxXQUFVOzs4RkFDYiw4REFBQzlCLG9LQUFhQTtvRkFBQzhCLFdBQVU7b0ZBQXVCUSxNQUFNOzs7Ozs7OEZBQ3RELDhEQUFDSDtvRkFBS0wsV0FBVTs4RkFBc0M7Ozs7Ozs7Ozs7OztzRkFFeEQsOERBQUN0Qyx1REFBS0E7NEVBQUNnRCxTQUFROzRFQUFVVixXQUFVO3NGQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0VBSWpELDhEQUFDRDs0REFBSUMsV0FBVTtzRUFDYiw0RUFBQ087Z0VBQUVQLFdBQVU7O2tGQUNYLDhEQUFDd0M7d0VBQU94QyxXQUFVO2tGQUFnQjs7Ozs7O29FQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBTTFELDhEQUFDMkIsT0FBTzVCLEdBQUc7Z0RBQ1Q2QixTQUFTO29EQUFFQyxTQUFTO29EQUFHVSxHQUFHO2dEQUFHO2dEQUM3QlIsYUFBYTtvREFBRUYsU0FBUztvREFBR1UsR0FBRztnREFBRTtnREFDaENQLFlBQVk7b0RBQUVDLFVBQVU7b0RBQUtDLE9BQU87Z0RBQUk7Z0RBQ3hDQyxVQUFVO29EQUFFQyxNQUFNO2dEQUFLO2dEQUN2QkMsWUFBWTtvREFBRVAsR0FBRyxDQUFDO29EQUFHRSxZQUFZO3dEQUFFQyxVQUFVO29EQUFJO2dEQUFFOzBEQUVuRCw0RUFBQ2xDO29EQUFJQyxXQUFVOztzRUFFYiw4REFBQ0Q7NERBQUlDLFdBQVU7Ozs7OztzRUFHZiw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDRDtvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNEOzRFQUFJQyxXQUFVO3NGQUNiLDRFQUFDeUM7Z0ZBQUl6QyxXQUFVO2dGQUFxQjBDLE1BQUs7Z0ZBQWVDLFNBQVE7MEZBQzlELDRFQUFDQztvRkFBS0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7OztzRkFHWiw4REFBQzlDOzRFQUFJQyxXQUFVO3NGQUNiLDRFQUFDaEMsb0tBQVdBO2dGQUFDZ0MsV0FBVTtnRkFBYVEsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7OEVBSTlDLDhEQUFDVDtvRUFBSUMsV0FBVTs4RUFBbUM7Ozs7Ozs4RUFFbEQsOERBQUNEO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQ0Q7NEVBQUlDLFdBQVU7c0ZBQ2IsNEVBQUNsQyxvS0FBU0E7Z0ZBQUNrQyxXQUFVO2dGQUFhUSxNQUFNOzs7Ozs7Ozs7OztzRkFFMUMsOERBQUNUOzRFQUFJQyxXQUFVO3NGQUNiLDRFQUFDaEMsb0tBQVdBO2dGQUFDZ0MsV0FBVTtnRkFBYVEsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0VBS2hELDhEQUFDVDs0REFBSUMsV0FBVTs7OEVBQ2IsOERBQUNzQztvRUFBR3RDLFdBQVU7OEVBQXVDOzs7Ozs7OEVBQ3JELDhEQUFDRDtvRUFBSUMsV0FBVTs4RUFBa0c7Ozs7Ozs7Ozs7OztzRUFLbkgsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQ0Q7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDRDs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUNoQyxvS0FBV0E7b0ZBQUNnQyxXQUFVO29GQUFzQlEsTUFBTTs7Ozs7OzhGQUNuRCw4REFBQ0g7b0ZBQUtMLFdBQVU7OEZBQXFDOzs7Ozs7Ozs7Ozs7c0ZBRXZELDhEQUFDdEMsdURBQUtBOzRFQUFDZ0QsU0FBUTs0RUFBVVYsV0FBVTtzRkFBVTs7Ozs7Ozs7Ozs7OzhFQUcvQyw4REFBQ0Q7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDRDs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUNoQyxvS0FBV0E7b0ZBQUNnQyxXQUFVO29GQUFzQlEsTUFBTTs7Ozs7OzhGQUNuRCw4REFBQ0g7b0ZBQUtMLFdBQVU7OEZBQXFDOzs7Ozs7Ozs7Ozs7c0ZBRXZELDhEQUFDdEMsdURBQUtBOzRFQUFDZ0QsU0FBUTs0RUFBVVYsV0FBVTtzRkFBVTs7Ozs7Ozs7Ozs7OzhFQUcvQyw4REFBQ0Q7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDRDs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUNoQyxvS0FBV0E7b0ZBQUNnQyxXQUFVO29GQUFzQlEsTUFBTTs7Ozs7OzhGQUNuRCw4REFBQ0g7b0ZBQUtMLFdBQVU7OEZBQXFDOzs7Ozs7Ozs7Ozs7c0ZBRXZELDhEQUFDdEMsdURBQUtBOzRFQUFDZ0QsU0FBUTs0RUFBVVYsV0FBVTtzRkFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NFQUlqRCw4REFBQ0Q7NERBQUlDLFdBQVU7c0VBQ2IsNEVBQUNPO2dFQUFFUCxXQUFVOztrRkFDWCw4REFBQ3dDO3dFQUFPeEMsV0FBVTtrRkFBZ0I7Ozs7OztvRUFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQU81RCw4REFBQzJCLE9BQU81QixHQUFHO3dDQUNUNkIsU0FBUzs0Q0FBRUMsU0FBUzs0Q0FBR0MsR0FBRzt3Q0FBRzt3Q0FDN0JDLGFBQWE7NENBQUVGLFNBQVM7NENBQUdDLEdBQUc7d0NBQUU7d0NBQ2hDRSxZQUFZOzRDQUFFQyxVQUFVOzRDQUFLQyxPQUFPO3dDQUFJO3dDQUN4Q0MsVUFBVTs0Q0FBRUMsTUFBTTt3Q0FBSzt3Q0FDdkJwQyxXQUFVOzswREFFViw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTs7Ozs7O2tFQUNmLDhEQUFDdkMseURBQU1BO3dEQUNMK0MsTUFBSzt3REFDTEMsU0FBUyxJQUFNM0IsZUFBZTt3REFDOUJrQixXQUFVOzs0REFDWDswRUFFQyw4REFBQzdCLG1LQUFVQTtnRUFBQzZCLFdBQVU7Z0VBQU9RLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFHdkMsOERBQUNEO2dEQUFFUCxXQUFVOzBEQUE2Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7dUJBTWxELHdCQUF3QjswQkFDeEIsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUNyQyxvS0FBTUE7b0RBQUNxQyxXQUFVO29EQUFhUSxNQUFNOzs7Ozs7Ozs7OzswREFFdkMsOERBQUNUOztrRUFDQyw4REFBQ087d0RBQUdOLFdBQVU7a0VBQTBHOzs7Ozs7a0VBR3hILDhEQUFDTzt3REFBRVAsV0FBVTtrRUFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFNekMsOERBQUN2Qyx5REFBTUE7d0NBQ0xpRCxTQUFRO3dDQUNSRCxTQUFTOzRDQUNQM0IsZUFBZTs0Q0FDZkosdUJBQXVCOzRDQUN2Qkosa0JBQWtCOzRDQUNsQkUscUJBQXFCO3dDQUN2Qjt3Q0FDQXdCLFdBQVU7a0RBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBT1AsOERBQUM4Qzt3QkFBSzlDLFdBQVU7a0NBQ2IsQ0FBQ3ZCLG9DQUNBLDhEQUFDc0I7NEJBQUlDLFdBQVU7OzhDQUViLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQzFDLHNFQUFjQTt3Q0FDYnlGLGdCQUFnQnpFO3dDQUNoQkQsZ0JBQWdCQTs7Ozs7Ozs7Ozs7OENBS3BCLDhEQUFDMEI7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUN6Qyw0RUFBaUJBO3dDQUNoQnlGLG1CQUFtQnhFO3dDQUNuQkQsbUJBQW1CQTs7Ozs7Ozs7Ozs7Z0NBS3RCRixrQkFBa0JFLG1DQUNqQiw4REFBQ3dCO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDdkMseURBQU1BO3dDQUNMZ0QsU0FBUzFCO3dDQUNUSixTQUFTQTt3Q0FDVDZCLE1BQUs7d0NBQ0xSLFdBQVU7OzBEQUVWLDhEQUFDckMsb0tBQU1BO2dEQUFDcUMsV0FBVTtnREFBT1EsTUFBTTs7Ozs7OzRDQUFNOzs7Ozs7Ozs7Ozs7Ozs7OztpREFPN0MsOERBQUNUOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUN2Qyx5REFBTUE7d0NBQ0xnRCxTQUFTWDt3Q0FDVFksU0FBUTt3Q0FDUkYsTUFBSztrREFDTjs7Ozs7Ozs7Ozs7OENBSUgsOERBQUNoRCxnRkFBbUJBO29DQUFDa0MsTUFBTWpCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFVM0M7R0E5Z0J3Qkw7S0FBQUEiLCJzb3VyY2VzIjpbIkQ6XFxXZWJQcm9qZWN0XFxwcm9kdWN0ZmluZGVyXFxzcmNcXGFwcFxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IE5vU1NSIH0gZnJvbSAnQC9jb21wb25lbnRzL05vU1NSJztcbmltcG9ydCB7IERldmljZVNlbGVjdG9yIH0gZnJvbSAnQC9jb21wb25lbnRzL0RldmljZVNlbGVjdG9yJztcbmltcG9ydCB7IEFjY2Vzc29yeVNlbGVjdG9yIH0gZnJvbSAnQC9jb21wb25lbnRzL0FjY2Vzc29yeVNlbGVjdG9yJztcbmltcG9ydCB7IENvbXBhdGliaWxpdHlSZXN1bHQgfSBmcm9tICdAL2NvbXBvbmVudHMvQ29tcGF0aWJpbGl0eVJlc3VsdCc7XG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvQnV0dG9uJztcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkRGVzY3JpcHRpb24sIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9DYXJkJztcbmltcG9ydCB7IEJhZGdlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL0JhZGdlJztcbmltcG9ydCB7IFNlYXJjaCwgU21hcnRwaG9uZSwgSGVhZHBob25lcywgSGFyZERyaXZlLCBaYXAsIENoZWNrQ2lyY2xlLCBYQ2lyY2xlLCBBbGVydFRyaWFuZ2xlLCBBcnJvd1JpZ2h0IH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcblxuaW50ZXJmYWNlIERldmljZSB7XG4gIF9pZDogc3RyaW5nO1xuICBuYW1lOiBzdHJpbmc7XG4gIGJyYW5kOiBzdHJpbmc7XG4gIHR5cGU6IHN0cmluZztcbiAgc3BlY3M6IGFueTtcbn1cblxuaW50ZXJmYWNlIEFjY2Vzc29yeSB7XG4gIF9pZDogc3RyaW5nO1xuICBuYW1lOiBzdHJpbmc7XG4gIGJyYW5kOiBzdHJpbmc7XG4gIHR5cGU6IHN0cmluZztcbiAgc3BlY3M6IGFueTtcbiAgcHJpY2U/OiB7XG4gICAgYW1vdW50OiBudW1iZXI7XG4gICAgY3VycmVuY3k6IHN0cmluZztcbiAgfTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcbiAgY29uc3QgW3NlbGVjdGVkRGV2aWNlLCBzZXRTZWxlY3RlZERldmljZV0gPSB1c2VTdGF0ZTxEZXZpY2UgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3NlbGVjdGVkQWNjZXNzb3J5LCBzZXRTZWxlY3RlZEFjY2Vzc29yeV0gPSB1c2VTdGF0ZTxBY2Nlc3NvcnkgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2NvbXBhdGliaWxpdHlSZXN1bHQsIHNldENvbXBhdGliaWxpdHlSZXN1bHRdID0gdXNlU3RhdGU8YW55PihudWxsKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbc2hvd0NoZWNrZXIsIHNldFNob3dDaGVja2VyXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICBjb25zdCBjaGVja0NvbXBhdGliaWxpdHkgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFzZWxlY3RlZERldmljZSB8fCAhc2VsZWN0ZWRBY2Nlc3NvcnkpIHJldHVybjtcblxuICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvY2hlY2stY29tcGF0aWJpbGl0eScsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgZGV2aWNlSWQ6IHNlbGVjdGVkRGV2aWNlLl9pZCxcbiAgICAgICAgICBhY2Nlc3NvcnlJZDogc2VsZWN0ZWRBY2Nlc3NvcnkuX2lkLFxuICAgICAgICB9KSxcbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgc2V0Q29tcGF0aWJpbGl0eVJlc3VsdChkYXRhKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY2hlY2tpbmcgY29tcGF0aWJpbGl0eTonLCBlcnJvcik7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCByZXNldFNlbGVjdGlvbiA9ICgpID0+IHtcbiAgICBzZXRTZWxlY3RlZERldmljZShudWxsKTtcbiAgICBzZXRTZWxlY3RlZEFjY2Vzc29yeShudWxsKTtcbiAgICBzZXRDb21wYXRpYmlsaXR5UmVzdWx0KG51bGwpO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPE5vU1NSPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1zbGF0ZS01MCB2aWEtYmx1ZS01MCB0by1pbmRpZ28tMTAwXCI+XG4gICAgICAgIHshc2hvd0NoZWNrZXIgPyAoXG4gICAgICAgIC8vIExhbmRpbmcgUGFnZVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgey8qIEhlcm8gU2VjdGlvbiAqL31cbiAgICAgICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBvdmVyZmxvdy1oaWRkZW4gYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1pbmRpZ28tOTAwIHZpYS1ibHVlLTkwMCB0by1wdXJwbGUtOTAwXCI+XG4gICAgICAgICAgICB7LyogQmFja2dyb3VuZCBQYXR0ZXJuICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIG9wYWNpdHktMjBcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wXCIgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kSW1hZ2U6IGByYWRpYWwtZ3JhZGllbnQoY2lyY2xlIGF0IDI1cHggMjVweCwgcmdiYSgyNTUsMjU1LDI1NSwwLjEpIDJweCwgdHJhbnNwYXJlbnQgMClgLFxuICAgICAgICAgICAgICAgIGJhY2tncm91bmRTaXplOiAnNTBweCA1MHB4J1xuICAgICAgICAgICAgICB9fT48L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIG1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04IHB5LTIwIGxnOnB5LTMyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICB7LyogQmFkZ2UgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtNCBweS0yIHJvdW5kZWQtZnVsbCBiZy13aGl0ZS8xMCBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlciBib3JkZXItd2hpdGUvMjAgdGV4dC13aGl0ZS85MCB0ZXh0LXNtIGZvbnQtbWVkaXVtIG1iLThcIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInctMiBoLTIgYmctZ3JlZW4tNDAwIHJvdW5kZWQtZnVsbCBtci0yIGFuaW1hdGUtcHVsc2VcIj48L3NwYW4+XG4gICAgICAgICAgICAgICAgICBTYXZlIE1vbmV5IOKAoiBBdm9pZCBJbmNvbXBhdGlibGUgUHVyY2hhc2VzXG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC01eGwgbWQ6dGV4dC03eGwgZm9udC1leHRyYWJvbGQgbWItOCBiZy1ncmFkaWVudC10by1yIGZyb20td2hpdGUgdmlhLWJsdWUtMTAwIHRvLXB1cnBsZS0yMDAgYmctY2xpcC10ZXh0IHRleHQtdHJhbnNwYXJlbnQgbGVhZGluZy10aWdodFwiPlxuICAgICAgICAgICAgICAgICAgRG9uJ3QgV2FzdGUgTW9uZXkgb24gRmVhdHVyZXN7JyAnfVxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tciBmcm9tLXllbGxvdy00MDAgdG8tb3JhbmdlLTQwMCBiZy1jbGlwLXRleHQgdGV4dC10cmFuc3BhcmVudFwiPlxuICAgICAgICAgICAgICAgICAgICBZb3UgQ2FuJ3QgVXNlXG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9oMT5cblxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteGwgbWQ6dGV4dC0yeGwgbWItMTIgdGV4dC1ibHVlLTEwMCBtYXgtdy00eGwgbXgtYXV0byBsZWFkaW5nLXJlbGF4ZWRcIj5cbiAgICAgICAgICAgICAgICAgIEdldCBpbnN0YW50IGNvbXBhdGliaWxpdHkgYW5hbHlzaXMgZm9yIHlvdXIgdGVjaCBwdXJjaGFzZXMuIEtub3cgZXhhY3RseSB3aGF0IGZlYXR1cmVzIHdpbGwgd29yayB3aXRoIHlvdXIgZGV2aWNlcyBiZWZvcmUgeW91IGJ1eS5cbiAgICAgICAgICAgICAgICA8L3A+XG5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc206ZmxleC1yb3cgZ2FwLTQganVzdGlmeS1jZW50ZXIgaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIHNpemU9XCJsZ1wiXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dDaGVja2VyKHRydWUpfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1yIGZyb20teWVsbG93LTQwMCB0by1vcmFuZ2UtNTAwIHRleHQtYmxhY2sgaG92ZXI6ZnJvbS15ZWxsb3ctMzAwIGhvdmVyOnRvLW9yYW5nZS00MDAgdGV4dC1sZyBweC04IHB5LTQgZm9udC1zZW1pYm9sZCBzaGFkb3ctMnhsIGhvdmVyOnNoYWRvdy15ZWxsb3ctNTAwLzI1IHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCB0cmFuc2Zvcm0gaG92ZXI6c2NhbGUtMTA1XCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgQ2hlY2sgQ29tcGF0aWJpbGl0eSBOb3dcbiAgICAgICAgICAgICAgICAgICAgPEFycm93UmlnaHQgY2xhc3NOYW1lPVwibWwtMlwiIHNpemU9ezIwfSAvPlxuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG5cbiAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgICBzaXplPVwibGdcIlxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJib3JkZXItd2hpdGUvMzAgdGV4dC13aGl0ZSBob3ZlcjpiZy13aGl0ZS8xMCBiYWNrZHJvcC1ibHVyLXNtIHB4LTggcHktNFwiXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnaG93LWl0LXdvcmtzJyk/LnNjcm9sbEludG9WaWV3KHsgYmVoYXZpb3I6ICdzbW9vdGgnIH0pO1xuICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICBMZWFybiBIb3cgSXQgV29ya3NcbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qIFN0YXRzICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMyBnYXAtOCBtdC0xNiBtYXgtdy0yeGwgbXgtYXV0b1wiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTJcIj4xMDAwKzwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtYmx1ZS0yMDAgdGV4dC1zbVwiPkRldmljZXMgU3VwcG9ydGVkPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBtYi0yXCI+OTUlPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTIwMCB0ZXh0LXNtXCI+QWNjdXJhY3kgUmF0ZTwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItMlwiPiQ1MDArPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTIwMCB0ZXh0LXNtXCI+QXZlcmFnZSBTYXZpbmdzPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIEZsb2F0aW5nIEljb25zIEFuaW1hdGlvbiAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBvdmVyZmxvdy1oaWRkZW4gcG9pbnRlci1ldmVudHMtbm9uZVwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0yMCBsZWZ0LTEwIGxnOmxlZnQtMjAgYW5pbWF0ZS1ib3VuY2VcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBiZy13aGl0ZS8xMCBiYWNrZHJvcC1ibHVyLXNtIHJvdW5kZWQtMnhsIGJvcmRlciBib3JkZXItd2hpdGUvMjBcIj5cbiAgICAgICAgICAgICAgICAgIDxTbWFydHBob25lIHNpemU9ezMyfSBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtMzAwXCIgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMzIgcmlnaHQtMTAgbGc6cmlnaHQtMjAgYW5pbWF0ZS1wdWxzZVwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGJnLXdoaXRlLzEwIGJhY2tkcm9wLWJsdXItc20gcm91bmRlZC0yeGwgYm9yZGVyIGJvcmRlci13aGl0ZS8yMFwiPlxuICAgICAgICAgICAgICAgICAgPEhlYWRwaG9uZXMgc2l6ZT17Mjh9IGNsYXNzTmFtZT1cInRleHQtcHVycGxlLTMwMFwiIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgYm90dG9tLTMyIGxlZnQtMTYgbGc6bGVmdC0zMiBhbmltYXRlLWJvdW5jZVwiIHN0eWxlPXt7IGFuaW1hdGlvbkRlbGF5OiAnMXMnIH19PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGJnLXdoaXRlLzEwIGJhY2tkcm9wLWJsdXItc20gcm91bmRlZC0yeGwgYm9yZGVyIGJvcmRlci13aGl0ZS8yMFwiPlxuICAgICAgICAgICAgICAgICAgPEhhcmREcml2ZSBzaXplPXszMH0gY2xhc3NOYW1lPVwidGV4dC1ncmVlbi0zMDBcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS0yMCByaWdodC0xNiBsZzpyaWdodC0zMiBhbmltYXRlLXB1bHNlXCIgc3R5bGU9e3sgYW5pbWF0aW9uRGVsYXk6ICcycycgfX0+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgYmctd2hpdGUvMTAgYmFja2Ryb3AtYmx1ci1zbSByb3VuZGVkLTJ4bCBib3JkZXIgYm9yZGVyLXdoaXRlLzIwXCI+XG4gICAgICAgICAgICAgICAgICA8WmFwIHNpemU9ezI2fSBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy0zMDBcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgICAgIHsvKiBIb3cgSXQgV29ya3MgU2VjdGlvbiAqL31cbiAgICAgICAgICA8c2VjdGlvbiBpZD1cImhvdy1pdC13b3Jrc1wiIGNsYXNzTmFtZT1cInB5LTI0IGJnLXdoaXRlIHJlbGF0aXZlIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgICAgey8qIEJhY2tncm91bmQgRWxlbWVudHMgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0wIGxlZnQtMCB3LWZ1bGwgaC1mdWxsXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTIwIGxlZnQtMTAgdy03MiBoLTcyIGJnLWJsdWUtMTAwIHJvdW5kZWQtZnVsbCBtaXgtYmxlbmQtbXVsdGlwbHkgZmlsdGVyIGJsdXIteGwgb3BhY2l0eS0zMCBhbmltYXRlLXB1bHNlXCI+PC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTQwIHJpZ2h0LTEwIHctNzIgaC03MiBiZy1wdXJwbGUtMTAwIHJvdW5kZWQtZnVsbCBtaXgtYmxlbmQtbXVsdGlwbHkgZmlsdGVyIGJsdXIteGwgb3BhY2l0eS0zMCBhbmltYXRlLXB1bHNlXCIgc3R5bGU9e3sgYW5pbWF0aW9uRGVsYXk6ICcycycgfX0+PC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgYm90dG9tLTIwIGxlZnQtMS8yIHctNzIgaC03MiBiZy1pbmRpZ28tMTAwIHJvdW5kZWQtZnVsbCBtaXgtYmxlbmQtbXVsdGlwbHkgZmlsdGVyIGJsdXIteGwgb3BhY2l0eS0zMCBhbmltYXRlLXB1bHNlXCIgc3R5bGU9e3sgYW5pbWF0aW9uRGVsYXk6ICc0cycgfX0+PC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTIwXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtNCBweS0yIHJvdW5kZWQtZnVsbCBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS0xMDAgdG8tcHVycGxlLTEwMCB0ZXh0LWJsdWUtODAwIHRleHQtc20gZm9udC1tZWRpdW0gbWItNlwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1ibHVlLTUwMCByb3VuZGVkLWZ1bGwgbXItMlwiPjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIFNpbXBsZSBQcm9jZXNzXG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtNHhsIG1kOnRleHQtNXhsIGZvbnQtYm9sZCBiZy1ncmFkaWVudC10by1yIGZyb20tZ3JheS05MDAgdG8tZ3JheS02MDAgYmctY2xpcC10ZXh0IHRleHQtdHJhbnNwYXJlbnQgbWItNlwiPlxuICAgICAgICAgICAgICAgICAgSG93IEl0IFdvcmtzXG4gICAgICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIHRleHQtZ3JheS02MDAgbWF4LXctM3hsIG14LWF1dG8gbGVhZGluZy1yZWxheGVkXCI+XG4gICAgICAgICAgICAgICAgICBHZXQgZGV0YWlsZWQgY29tcGF0aWJpbGl0eSBhbmFseXNpcyBpbiBqdXN0IDMgc2ltcGxlIHN0ZXBzLiBPdXIgQUktcG93ZXJlZCBzeXN0ZW0gYW5hbHl6ZXMgdGhvdXNhbmRzIG9mIGRldmljZSBzcGVjaWZpY2F0aW9ucyBpbnN0YW50bHkuXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgbWQ6Z3JpZC1jb2xzLTMgZ2FwLTggbGc6Z2FwLTEyXCI+XG4gICAgICAgICAgICAgICAge1tcbiAgICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgICAgc3RlcDogXCIwMVwiLFxuICAgICAgICAgICAgICAgICAgICB0aXRsZTogXCJTZWxlY3QgWW91ciBEZXZpY2VcIixcbiAgICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb246IFwiQ2hvb3NlIHlvdXIgc21hcnRwaG9uZSwgbGFwdG9wLCBvciB0YWJsZXQgZnJvbSBvdXIgY29tcHJlaGVuc2l2ZSBkYXRhYmFzZSBvZiAxMDAwKyBkZXZpY2VzXCIsXG4gICAgICAgICAgICAgICAgICAgIGljb246IFNtYXJ0cGhvbmUsXG4gICAgICAgICAgICAgICAgICAgIGdyYWRpZW50OiBcImZyb20tYmx1ZS01MDAgdG8tY3lhbi01MDBcIixcbiAgICAgICAgICAgICAgICAgICAgYmdHcmFkaWVudDogXCJmcm9tLWJsdWUtNTAgdG8tY3lhbi01MFwiXG4gICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgICBzdGVwOiBcIjAyXCIsXG4gICAgICAgICAgICAgICAgICAgIHRpdGxlOiBcIlBpY2sgYW4gQWNjZXNzb3J5XCIsXG4gICAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBcIlNlbGVjdCB0aGUgYWNjZXNzb3J5IHlvdSB3YW50IHRvIGNoZWNrIC0gVFdTIGVhcmJ1ZHMsIFNTRCwgY2hhcmdlciwgY2FibGUsIG1vbml0b3IsIG9yIHNvdW5kYmFyXCIsXG4gICAgICAgICAgICAgICAgICAgIGljb246IEhlYWRwaG9uZXMsXG4gICAgICAgICAgICAgICAgICAgIGdyYWRpZW50OiBcImZyb20tcHVycGxlLTUwMCB0by1waW5rLTUwMFwiLFxuICAgICAgICAgICAgICAgICAgICBiZ0dyYWRpZW50OiBcImZyb20tcHVycGxlLTUwIHRvLXBpbmstNTBcIlxuICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgICAgc3RlcDogXCIwM1wiLFxuICAgICAgICAgICAgICAgICAgICB0aXRsZTogXCJHZXQgU21hcnQgUmVzdWx0c1wiLFxuICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogXCJSZWNlaXZlIGRldGFpbGVkIGNvbXBhdGliaWxpdHkgYW5hbHlzaXMgd2l0aCBBSSBleHBsYW5hdGlvbnMsIHJlY29tbWVuZGF0aW9ucywgYW5kIGRpcmVjdCBwdXJjaGFzZSBsaW5rc1wiLFxuICAgICAgICAgICAgICAgICAgICBpY29uOiBDaGVja0NpcmNsZSxcbiAgICAgICAgICAgICAgICAgICAgZ3JhZGllbnQ6IFwiZnJvbS1ncmVlbi01MDAgdG8tZW1lcmFsZC01MDBcIixcbiAgICAgICAgICAgICAgICAgICAgYmdHcmFkaWVudDogXCJmcm9tLWdyZWVuLTUwIHRvLWVtZXJhbGQtNTBcIlxuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIF0ubWFwKChpdGVtLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICAgICAga2V5PXtpbmRleH1cbiAgICAgICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAzMCB9fVxuICAgICAgICAgICAgICAgICAgICB3aGlsZUluVmlldz17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuOCwgZGVsYXk6IGluZGV4ICogMC4yIH19XG4gICAgICAgICAgICAgICAgICAgIHZpZXdwb3J0PXt7IG9uY2U6IHRydWUgfX1cbiAgICAgICAgICAgICAgICAgICAgd2hpbGVIb3Zlcj17eyB5OiAtOCwgdHJhbnNpdGlvbjogeyBkdXJhdGlvbjogMC4zIH0gfX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2ByZWxhdGl2ZSBwLTggcm91bmRlZC0zeGwgYmctZ3JhZGllbnQtdG8tYnIgJHtpdGVtLmJnR3JhZGllbnR9IGJvcmRlciBib3JkZXItd2hpdGUvNTAgc2hhZG93LXhsIGhvdmVyOnNoYWRvdy0yeGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGgtZnVsbCBncm91cGB9PlxuICAgICAgICAgICAgICAgICAgICAgIHsvKiBTdGVwIE51bWJlciAqL31cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIC10b3AtNCAtbGVmdC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHctMTIgaC0xMiByb3VuZGVkLTJ4bCBiZy1ncmFkaWVudC10by1yICR7aXRlbS5ncmFkaWVudH0gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdGV4dC13aGl0ZSBmb250LWJvbGQgdGV4dC1sZyBzaGFkb3ctbGdgfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2l0ZW0uc3RlcH1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgey8qIEljb24gKi99XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2B3LTIwIGgtMjAgcm91bmRlZC0yeGwgYmctZ3JhZGllbnQtdG8tciAke2l0ZW0uZ3JhZGllbnR9IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG14LWF1dG8gbWItNiBncm91cC1ob3ZlcjpzY2FsZS0xMTAgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMzAwYH0+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aXRlbS5pY29uIHNpemU9ezM2fSBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgIHsvKiBDb250ZW50ICovfVxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+e2l0ZW0udGl0bGV9PC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbGVhZGluZy1yZWxheGVkXCI+e2l0ZW0uZGVzY3JpcHRpb259PC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgey8qIERlY29yYXRpdmUgRWxlbWVudHMgKi99XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtNCByaWdodC00IHctOCBoLTggYmctd2hpdGUvMjAgcm91bmRlZC1mdWxsXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBib3R0b20tNCBsZWZ0LTQgdy02IGgtNiBiZy13aGl0ZS8xMCByb3VuZGVkLWZ1bGxcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICAgICAgey8qIEV4YW1wbGUgQ2FzZXMgU2VjdGlvbiAqL31cbiAgICAgICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJweS0yNCBiZy1ncmFkaWVudC10by1iciBmcm9tLWdyYXktNTAgdG8tYmx1ZS01MCByZWxhdGl2ZVwiPlxuICAgICAgICAgICAgey8qIEJhY2tncm91bmQgUGF0dGVybiAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBvcGFjaXR5LTMwXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMFwiIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZEltYWdlOiBgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCAyMHB4IDIwcHgsIHJnYmEoMCwwLDAsMC4wMikgMTBweCwgdHJhbnNwYXJlbnQgMClgLFxuICAgICAgICAgICAgICAgIGJhY2tncm91bmRTaXplOiAnNDBweCA0MHB4J1xuICAgICAgICAgICAgICB9fT48L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIG1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAzMCB9fVxuICAgICAgICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjggfX1cbiAgICAgICAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWItMjBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtNCBweS0yIHJvdW5kZWQtZnVsbCBiZy1ncmFkaWVudC10by1yIGZyb20taW5kaWdvLTEwMCB0by1ibHVlLTEwMCB0ZXh0LWluZGlnby04MDAgdGV4dC1zbSBmb250LW1lZGl1bSBtYi02XCI+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLWluZGlnby01MDAgcm91bmRlZC1mdWxsIG1yLTJcIj48L3NwYW4+XG4gICAgICAgICAgICAgICAgICBSZWFsIEV4YW1wbGVzXG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtNHhsIG1kOnRleHQtNXhsIGZvbnQtYm9sZCBiZy1ncmFkaWVudC10by1yIGZyb20tZ3JheS05MDAgdG8tZ3JheS02MDAgYmctY2xpcC10ZXh0IHRleHQtdHJhbnNwYXJlbnQgbWItNlwiPlxuICAgICAgICAgICAgICAgICAgU2VlIEl0IEluIEFjdGlvblxuICAgICAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14bCB0ZXh0LWdyYXktNjAwIG1heC13LTN4bCBteC1hdXRvIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICAgICAgUmVhbCBjb21wYXRpYmlsaXR5IHNjZW5hcmlvcyBzaG93aW5nIGhvdyBvdXIgdG9vbCBwcmV2ZW50cyBjb3N0bHkgbWlzdGFrZXMgYW5kIGhlbHBzIHlvdSBtYWtlIGluZm9ybWVkIGRlY2lzaW9ucy5cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgbGc6Z3JpZC1jb2xzLTIgZ2FwLTggbGc6Z2FwLTEyXCI+XG4gICAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogLTMwIH19XG4gICAgICAgICAgICAgICAgICB3aGlsZUluVmlldz17eyBvcGFjaXR5OiAxLCB4OiAwIH19XG4gICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjggfX1cbiAgICAgICAgICAgICAgICAgIHZpZXdwb3J0PXt7IG9uY2U6IHRydWUgfX1cbiAgICAgICAgICAgICAgICAgIHdoaWxlSG92ZXI9e3sgeTogLTUsIHRyYW5zaXRpb246IHsgZHVyYXRpb246IDAuMyB9IH19XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBwLTggYmctd2hpdGUgcm91bmRlZC0zeGwgc2hhZG93LXhsIGhvdmVyOnNoYWRvdy0yeGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGJvcmRlciBib3JkZXItZ3JheS0xMDAgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgICAgICAgICAgIHsvKiBCYWNrZ3JvdW5kIEdyYWRpZW50ICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0wIHJpZ2h0LTAgdy0zMiBoLTMyIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS0xMDAgdG8tcHVycGxlLTEwMCByb3VuZGVkLWZ1bGwgLXRyYW5zbGF0ZS15LTE2IHRyYW5zbGF0ZS14LTE2IG9wYWNpdHktNTBcIj48L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICB7LyogRGV2aWNlIEljb25zICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNwYWNlLXgtNiBtYi04XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNTAwIHRvLWJsdWUtNjAwIHJvdW5kZWQtMnhsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNoYWRvdy1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8U21hcnRwaG9uZSBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlXCIgc2l6ZT17Mjh9IC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLWJvdHRvbS0yIC1yaWdodC0yIHctNiBoLTYgYmctZ3JlZW4tNTAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGUgY2xhc3NOYW1lPVwidGV4dC13aGl0ZVwiIHNpemU9ezE0fSAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWdyYXktMzAwXCI+KzwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgYmctZ3JhZGllbnQtdG8tciBmcm9tLXB1cnBsZS01MDAgdG8tcHVycGxlLTYwMCByb3VuZGVkLTJ4bCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBzaGFkb3ctbGdcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEhlYWRwaG9uZXMgY2xhc3NOYW1lPVwidGV4dC13aGl0ZVwiIHNpemU9ezI4fSAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIC1ib3R0b20tMiAtcmlnaHQtMiB3LTYgaC02IGJnLXllbGxvdy01MDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxBbGVydFRyaWFuZ2xlIGNsYXNzTmFtZT1cInRleHQtd2hpdGVcIiBzaXplPXsxNH0gLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi0yXCI+aVBob25lIDE1IFBybyArIFNvbnkgV0YtMTAwMFhNNDwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtMyBweS0xIHJvdW5kZWQtZnVsbCBiZy15ZWxsb3ctMTAwIHRleHQteWVsbG93LTgwMCB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA3NSUgQ29tcGF0aWJsZVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMyBtYi02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcC0zIGJnLWdyZWVuLTUwIHJvdW5kZWQteGxcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPENoZWNrQ2lyY2xlIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNjAwIG1yLTJcIiBzaXplPXsxNn0gLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyZWVuLTgwMFwiPkFBQyBDb2RlYzwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9XCJzdWNjZXNzXCIgY2xhc3NOYW1lPVwidGV4dC14c1wiPldvcmtzPC9CYWRnZT5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtMyBiZy1yZWQtNTAgcm91bmRlZC14bFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8WENpcmNsZSBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDAgbXItMlwiIHNpemU9ezE2fSAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtcmVkLTgwMFwiPkxEQUMgU3VwcG9ydDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9XCJlcnJvclwiIGNsYXNzTmFtZT1cInRleHQteHNcIj5NaXNzaW5nPC9CYWRnZT5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtMyBiZy15ZWxsb3ctNTAgcm91bmRlZC14bFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8QWxlcnRUcmlhbmdsZSBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy02MDAgbXItMlwiIHNpemU9ezE2fSAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQteWVsbG93LTgwMFwiPkhpLVJlcyBBdWRpbzwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9XCJ3YXJuaW5nXCIgY2xhc3NOYW1lPVwidGV4dC14c1wiPkxpbWl0ZWQ8L0JhZGdlPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBiZy1ncmF5LTUwIHJvdW5kZWQteGxcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgbGVhZGluZy1yZWxheGVkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Ryb25nIGNsYXNzTmFtZT1cInRleHQtZ3JheS05MDBcIj5SZXN1bHQ6PC9zdHJvbmc+IEdyZWF0IHNvdW5kIHF1YWxpdHkgd2l0aCBBQUMsIGJ1dCB5b3Ugd29uJ3QgZ2V0IExEQUMncyBoaS1yZXMgYXVkaW8gYmVuZWZpdHMuIENvbnNpZGVyIGFsdGVybmF0aXZlcyBmb3IgYXVkaW9waGlsZSBleHBlcmllbmNlLlxuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB4OiAzMCB9fVxuICAgICAgICAgICAgICAgICAgd2hpbGVJblZpZXc9e3sgb3BhY2l0eTogMSwgeDogMCB9fVxuICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC44LCBkZWxheTogMC4yIH19XG4gICAgICAgICAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19XG4gICAgICAgICAgICAgICAgICB3aGlsZUhvdmVyPXt7IHk6IC01LCB0cmFuc2l0aW9uOiB7IGR1cmF0aW9uOiAwLjMgfSB9fVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgcC04IGJnLXdoaXRlIHJvdW5kZWQtM3hsIHNoYWRvdy14bCBob3ZlcjpzaGFkb3ctMnhsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBib3JkZXIgYm9yZGVyLWdyYXktMTAwIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgICAgICAgICAgICB7LyogQmFja2dyb3VuZCBHcmFkaWVudCAqL31cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMCBsZWZ0LTAgdy0zMiBoLTMyIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tZ3JlZW4tMTAwIHRvLWVtZXJhbGQtMTAwIHJvdW5kZWQtZnVsbCAtdHJhbnNsYXRlLXktMTYgLXRyYW5zbGF0ZS14LTE2IG9wYWNpdHktNTBcIj48L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICB7LyogRGV2aWNlIEljb25zICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNwYWNlLXgtNiBtYi04XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgYmctZ3JhZGllbnQtdG8tciBmcm9tLWdyYXktNjAwIHRvLWdyYXktNzAwIHJvdW5kZWQtMnhsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNoYWRvdy1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctOCBoLTggdGV4dC13aGl0ZVwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZD1cIk0yMCA0SDRjLTEuMSAwLTIgLjktMiAydjEyYzAgMS4xLjkgMiAyIDJoMTZjMS4xIDAgMi0uOSAyLTJWNmMwLTEuMS0uOS0yLTItMnptMCAxNEg0VjhoMTZ2MTB6XCIvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtYm90dG9tLTIgLXJpZ2h0LTIgdy02IGgtNiBiZy1ncmVlbi01MDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlXCIgc2l6ZT17MTR9IC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JheS0zMDBcIj4rPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTYgaC0xNiBiZy1ncmFkaWVudC10by1yIGZyb20tZ3JlZW4tNTAwIHRvLWVtZXJhbGQtNjAwIHJvdW5kZWQtMnhsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNoYWRvdy1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8SGFyZERyaXZlIGNsYXNzTmFtZT1cInRleHQtd2hpdGVcIiBzaXplPXsyOH0gLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtYm90dG9tLTIgLXJpZ2h0LTIgdy02IGgtNiBiZy1ncmVlbi01MDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlXCIgc2l6ZT17MTR9IC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi02XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItMlwiPk1hY0Jvb2sgUHJvIE0zICsgU2Ftc3VuZyA5ODAgUFJPPC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC0zIHB5LTEgcm91bmRlZC1mdWxsIGJnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAxMDAlIENvbXBhdGlibGVcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTMgbWItNlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtMyBiZy1ncmVlbi01MCByb3VuZGVkLXhsXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTYwMCBtci0yXCIgc2l6ZT17MTZ9IC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmVlbi04MDBcIj5OVk1lIEludGVyZmFjZTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9XCJzdWNjZXNzXCIgY2xhc3NOYW1lPVwidGV4dC14c1wiPlBlcmZlY3Q8L0JhZGdlPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcC0zIGJnLWdyZWVuLTUwIHJvdW5kZWQteGxcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPENoZWNrQ2lyY2xlIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNjAwIG1yLTJcIiBzaXplPXsxNn0gLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyZWVuLTgwMFwiPlBDSWUgNC4wIFN1cHBvcnQ8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxCYWRnZSB2YXJpYW50PVwic3VjY2Vzc1wiIGNsYXNzTmFtZT1cInRleHQteHNcIj5TdXBwb3J0ZWQ8L0JhZGdlPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcC0zIGJnLWdyZWVuLTUwIHJvdW5kZWQteGxcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPENoZWNrQ2lyY2xlIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNjAwIG1yLTJcIiBzaXplPXsxNn0gLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyZWVuLTgwMFwiPkZ1bGwgU3BlZWQgKDdHQi9zKTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9XCJzdWNjZXNzXCIgY2xhc3NOYW1lPVwidGV4dC14c1wiPk1heGltdW08L0JhZGdlPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBiZy1ncmVlbi01MCByb3VuZGVkLXhsXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHN0cm9uZyBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktOTAwXCI+UmVzdWx0Ojwvc3Ryb25nPiBQZXJmZWN0IG1hdGNoISBZb3UnbGwgZ2V0IHRoZSBmdWxsIDdHQi9zIHJlYWQgc3BlZWRzIGZvciBsaWdodG5pbmctZmFzdCBwZXJmb3JtYW5jZS4gSWRlYWwgZm9yIHZpZGVvIGVkaXRpbmcgYW5kIGxhcmdlIGZpbGUgdHJhbnNmZXJzLlxuICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAzMCB9fVxuICAgICAgICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjgsIGRlbGF5OiAwLjQgfX1cbiAgICAgICAgICAgICAgICB2aWV3cG9ydD17eyBvbmNlOiB0cnVlIH19XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbXQtMTZcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBpbmxpbmUtYmxvY2tcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS02MDAgdG8tcHVycGxlLTYwMCByb3VuZGVkLTJ4bCBibHVyLWxnIG9wYWNpdHktMzAgYW5pbWF0ZS1wdWxzZVwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBzaXplPVwibGdcIlxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93Q2hlY2tlcih0cnVlKX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicmVsYXRpdmUgYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNjAwIHRvLXB1cnBsZS02MDAgaG92ZXI6ZnJvbS1ibHVlLTcwMCBob3Zlcjp0by1wdXJwbGUtNzAwIHRleHQtd2hpdGUgcHgtOCBweS00IHRleHQtbGcgZm9udC1zZW1pYm9sZCBzaGFkb3cteGwgaG92ZXI6c2hhZG93LTJ4bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgdHJhbnNmb3JtIGhvdmVyOnNjYWxlLTEwNVwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIFRyeSBJdCBZb3Vyc2VsZiAtIEl0J3MgRnJlZVxuICAgICAgICAgICAgICAgICAgICA8QXJyb3dSaWdodCBjbGFzc05hbWU9XCJtbC0yXCIgc2l6ZT17MjB9IC8+XG4gICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDAgbXQtNFwiPk5vIHNpZ251cCByZXF1aXJlZCDigKIgSW5zdGFudCByZXN1bHRzPC9wPlxuICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L3NlY3Rpb24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKSA6IChcbiAgICAgICAgLy8gQ29tcGF0aWJpbGl0eSBDaGVja2VyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tc2xhdGUtNTAgdmlhLWJsdWUtNTAgdG8taW5kaWdvLTEwMFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUvODAgYmFja2Ryb3AtYmx1ci1tZCBzaGFkb3ctbGcgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwLzUwXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04IHB5LThcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGxnOmZsZXgtcm93IGxnOml0ZW1zLWNlbnRlciBsZzpqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBtYi00IGxnOm1iLTBcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMCBoLTEwIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTYwMCB0by1wdXJwbGUtNjAwIHJvdW5kZWQteGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgPFNlYXJjaCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlXCIgc2l6ZT17MjB9IC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBsZzp0ZXh0LTR4bCBmb250LWJvbGQgYmctZ3JhZGllbnQtdG8tciBmcm9tLWdyYXktOTAwIHRvLWdyYXktNjAwIGJnLWNsaXAtdGV4dCB0ZXh0LXRyYW5zcGFyZW50XCI+XG4gICAgICAgICAgICAgICAgICAgICAgQ29tcGF0aWJpbGl0eSBDaGVja2VyXG4gICAgICAgICAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIEdldCBpbnN0YW50IGNvbXBhdGliaWxpdHkgYW5hbHlzaXMgZm9yIHlvdXIgdGVjaCBwdXJjaGFzZXNcbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIHNldFNob3dDaGVja2VyKGZhbHNlKTtcbiAgICAgICAgICAgICAgICAgICAgc2V0Q29tcGF0aWJpbGl0eVJlc3VsdChudWxsKTtcbiAgICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWREZXZpY2UobnVsbCk7XG4gICAgICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkQWNjZXNzb3J5KG51bGwpO1xuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlci1ncmF5LTMwMCBob3ZlcjpiZy1ncmF5LTUwIHB4LTYgcHktM1wiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAg4oaQIEJhY2sgdG8gSG9tZVxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPG1haW4gY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LTggcHktOFwiPlxuICAgICAgICAgICAgeyFjb21wYXRpYmlsaXR5UmVzdWx0ID8gKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgbGc6Z3JpZC1jb2xzLTIgZ2FwLThcIj5cbiAgICAgICAgICAgICAgICB7LyogRGV2aWNlIFNlbGVjdGlvbiAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93LWxnIHAtNlwiPlxuICAgICAgICAgICAgICAgICAgPERldmljZVNlbGVjdG9yXG4gICAgICAgICAgICAgICAgICAgIG9uRGV2aWNlU2VsZWN0PXtzZXRTZWxlY3RlZERldmljZX1cbiAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWREZXZpY2U9e3NlbGVjdGVkRGV2aWNlfVxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIHsvKiBBY2Nlc3NvcnkgU2VsZWN0aW9uICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctbGcgcC02XCI+XG4gICAgICAgICAgICAgICAgICA8QWNjZXNzb3J5U2VsZWN0b3JcbiAgICAgICAgICAgICAgICAgICAgb25BY2Nlc3NvcnlTZWxlY3Q9e3NldFNlbGVjdGVkQWNjZXNzb3J5fVxuICAgICAgICAgICAgICAgICAgICBzZWxlY3RlZEFjY2Vzc29yeT17c2VsZWN0ZWRBY2Nlc3Nvcnl9XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qIENoZWNrIENvbXBhdGliaWxpdHkgQnV0dG9uICovfVxuICAgICAgICAgICAgICAgIHtzZWxlY3RlZERldmljZSAmJiBzZWxlY3RlZEFjY2Vzc29yeSAmJiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmNvbC1zcGFuLTIgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2NoZWNrQ29tcGF0aWJpbGl0eX1cbiAgICAgICAgICAgICAgICAgICAgICBsb2FkaW5nPXtsb2FkaW5nfVxuICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJsZ1wiXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtOCBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS02MDAgdG8tcHVycGxlLTYwMCBob3Zlcjpmcm9tLWJsdWUtNzAwIGhvdmVyOnRvLXB1cnBsZS03MDAgdGV4dC13aGl0ZSBzaGFkb3cteGxcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPFNlYXJjaCBjbGFzc05hbWU9XCJtci0yXCIgc2l6ZT17MjB9IC8+XG4gICAgICAgICAgICAgICAgICAgICAgQ2hlY2sgQ29tcGF0aWJpbGl0eVxuICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1sZyBwLTZcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTYgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17cmVzZXRTZWxlY3Rpb259XG4gICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAg4oaQIENoZWNrIEFub3RoZXIgQ29tYmluYXRpb25cbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxDb21wYXRpYmlsaXR5UmVzdWx0IGRhdGE9e2NvbXBhdGliaWxpdHlSZXN1bHR9IC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L21haW4+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgPC9kaXY+XG4gICAgPC9Ob1NTUj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwiTm9TU1IiLCJEZXZpY2VTZWxlY3RvciIsIkFjY2Vzc29yeVNlbGVjdG9yIiwiQ29tcGF0aWJpbGl0eVJlc3VsdCIsIkJ1dHRvbiIsIkJhZGdlIiwiU2VhcmNoIiwiU21hcnRwaG9uZSIsIkhlYWRwaG9uZXMiLCJIYXJkRHJpdmUiLCJaYXAiLCJDaGVja0NpcmNsZSIsIlhDaXJjbGUiLCJBbGVydFRyaWFuZ2xlIiwiQXJyb3dSaWdodCIsIkhvbWUiLCJzZWxlY3RlZERldmljZSIsInNldFNlbGVjdGVkRGV2aWNlIiwic2VsZWN0ZWRBY2Nlc3NvcnkiLCJzZXRTZWxlY3RlZEFjY2Vzc29yeSIsImNvbXBhdGliaWxpdHlSZXN1bHQiLCJzZXRDb21wYXRpYmlsaXR5UmVzdWx0IiwibG9hZGluZyIsInNldExvYWRpbmciLCJzaG93Q2hlY2tlciIsInNldFNob3dDaGVja2VyIiwiY2hlY2tDb21wYXRpYmlsaXR5IiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsImRldmljZUlkIiwiX2lkIiwiYWNjZXNzb3J5SWQiLCJkYXRhIiwianNvbiIsImVycm9yIiwiY29uc29sZSIsInJlc2V0U2VsZWN0aW9uIiwiZGl2IiwiY2xhc3NOYW1lIiwic2VjdGlvbiIsInN0eWxlIiwiYmFja2dyb3VuZEltYWdlIiwiYmFja2dyb3VuZFNpemUiLCJzcGFuIiwiaDEiLCJwIiwic2l6ZSIsIm9uQ2xpY2siLCJ2YXJpYW50IiwiZG9jdW1lbnQiLCJnZXRFbGVtZW50QnlJZCIsInNjcm9sbEludG9WaWV3IiwiYmVoYXZpb3IiLCJhbmltYXRpb25EZWxheSIsImlkIiwiaDIiLCJzdGVwIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImljb24iLCJncmFkaWVudCIsImJnR3JhZGllbnQiLCJtYXAiLCJpdGVtIiwiaW5kZXgiLCJtb3Rpb24iLCJpbml0aWFsIiwib3BhY2l0eSIsInkiLCJ3aGlsZUluVmlldyIsInRyYW5zaXRpb24iLCJkdXJhdGlvbiIsImRlbGF5Iiwidmlld3BvcnQiLCJvbmNlIiwid2hpbGVIb3ZlciIsImgzIiwieCIsInN0cm9uZyIsInN2ZyIsImZpbGwiLCJ2aWV3Qm94IiwicGF0aCIsImQiLCJtYWluIiwib25EZXZpY2VTZWxlY3QiLCJvbkFjY2Vzc29yeVNlbGVjdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});