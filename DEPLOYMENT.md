# Deployment Guide

## Local Development

### Prerequisites
- Node.js 18+
- MongoDB (local or cloud)

### Setup Steps
1. Clone the repository
2. Install dependencies: `npm install`
3. Copy environment file: `cp .env.example .env.local`
4. Update `.env.local` with your MongoDB URI
5. Start MongoDB service
6. Seed database: `npm run seed`
7. Start development server: `npm run dev`

## Production Deployment

### Vercel Deployment (Recommended)

1. **Prepare MongoDB Atlas**
   - Create a MongoDB Atlas account
   - Create a new cluster
   - Get connection string
   - Whitelist Vercel IPs or use 0.0.0.0/0

2. **Deploy to Vercel**
   ```bash
   # Install Vercel CLI
   npm i -g vercel
   
   # Deploy
   vercel
   ```

3. **Set Environment Variables in Vercel**
   - Go to Vercel Dashboard → Project → Settings → Environment Variables
   - Add:
     - `MONGODB_URI`: Your MongoDB Atlas connection string
     - `NEXTAUTH_SECRET`: Random secret key
     - `NEXTAUTH_URL`: Your production URL

4. **Seed Production Database**
   - Update `scripts/seed-data.js` with production MongoDB URI
   - Run locally: `node scripts/seed-data.js`
   - Or create a one-time API endpoint for seeding

### Alternative Hosting Options

#### Railway
1. Connect GitHub repository
2. Set environment variables
3. Deploy automatically

#### Netlify
1. Build command: `npm run build`
2. Publish directory: `.next`
3. Set environment variables

## Database Setup

### MongoDB Atlas (Production)
1. Create account at mongodb.com
2. Create new project and cluster
3. Create database user
4. Get connection string
5. Replace `<password>` and `<dbname>` in connection string

### Local MongoDB
```bash
# macOS with Homebrew
brew install mongodb-community
brew services start mongodb-community

# Windows
# Download and install MongoDB Community Server
# Start as Windows service

# Linux (Ubuntu)
sudo apt install mongodb
sudo systemctl start mongodb
```

## Environment Variables

### Required
- `MONGODB_URI`: MongoDB connection string
- `NEXTAUTH_SECRET`: Random secret for session encryption
- `NEXTAUTH_URL`: Your application URL

### Optional
- `NODE_ENV`: Set to 'production' for production builds

## Post-Deployment

1. **Test the application**
   - Visit your deployed URL
   - Test device and accessory selection
   - Verify compatibility checking works

2. **Add sample data**
   - Use the admin panel at `/admin`
   - Or run the seed script against production database

3. **Monitor performance**
   - Check Vercel Analytics
   - Monitor database performance in MongoDB Atlas

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Verify MongoDB URI is correct
   - Check network access in MongoDB Atlas
   - Ensure database user has proper permissions

2. **Build Errors**
   - Check all dependencies are installed
   - Verify TypeScript types are correct
   - Check for missing environment variables

3. **API Errors**
   - Check server logs in Vercel dashboard
   - Verify API routes are properly configured
   - Test database connection

### Performance Optimization

1. **Database Indexing**
   - Indexes are already defined in models
   - Monitor query performance in MongoDB Atlas

2. **Caching**
   - Consider adding Redis for caching
   - Use Next.js built-in caching features

3. **CDN**
   - Vercel provides CDN automatically
   - Consider image optimization for product images

## Security Considerations

1. **Environment Variables**
   - Never commit `.env.local` to version control
   - Use strong, unique secrets

2. **Database Security**
   - Use MongoDB Atlas network access controls
   - Regular security updates

3. **API Security**
   - Consider rate limiting
   - Input validation is implemented
   - Add authentication for admin routes in production

## Monitoring

1. **Application Monitoring**
   - Vercel Analytics for performance
   - Error tracking with Sentry (optional)

2. **Database Monitoring**
   - MongoDB Atlas monitoring
   - Set up alerts for performance issues

3. **Uptime Monitoring**
   - Use services like Pingdom or UptimeRobot
   - Monitor critical API endpoints
