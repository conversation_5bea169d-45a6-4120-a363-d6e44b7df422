import { NextRequest, NextResponse } from 'next/server';
import { GSMArenaScraper } from '@/lib/scraping/gsmarena';
import { AmazonScraper } from '@/lib/scraping/amazon';
import connectDB from '@/lib/database/connection';
import Device from '@/lib/database/models/Device';
import Accessory from '@/lib/database/models/Accessory';

export async function POST(request: NextRequest) {
  try {
    const { action, query, type } = await request.json();

    if (!action || !query) {
      return NextResponse.json(
        { error: 'Action and query are required' },
        { status: 400 }
      );
    }

    switch (action) {
      case 'search_devices':
        return await searchDevices(query);
      
      case 'scrape_device':
        return await scrapeDevice(query);
      
      case 'search_accessories':
        return await searchAccessories(query, type);
      
      case 'scrape_accessory':
        return await scrapeAccessory(query, type);
      
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Scraping error:', error);
    return NextResponse.json(
      { error: 'Scraping failed' },
      { status: 500 }
    );
  }
}

async function searchDevices(query: string) {
  try {
    const deviceLinks = await GSMArenaScraper.searchDevice(query);
    return NextResponse.json({ links: deviceLinks });
  } catch (error) {
    console.error('Error searching devices:', error);
    return NextResponse.json(
      { error: 'Failed to search devices' },
      { status: 500 }
    );
  }
}

async function scrapeDevice(url: string) {
  try {
    const deviceData = await GSMArenaScraper.scrapeDevice(url);
    
    if (!deviceData) {
      return NextResponse.json(
        { error: 'Failed to scrape device data' },
        { status: 404 }
      );
    }

    // Save to database
    await connectDB();
    
    const device = new Device({
      name: deviceData.name,
      brand: deviceData.brand,
      type: 'smartphone', // Default type, can be updated
      specs: {
        bluetooth: deviceData.bluetooth,
        supportedCodecs: deviceData.supportedCodecs,
        usbType: deviceData.usbType,
        powerDelivery: deviceData.chargingProtocols?.join(', '),
        displayPorts: deviceData.resolution ? ['Built-in Display'] : undefined,
        refreshRateSupport: deviceData.refreshRate
      },
      releaseYear: new Date().getFullYear()
    });

    await device.save();
    
    return NextResponse.json({ 
      message: 'Device scraped and saved successfully',
      device: deviceData 
    });
  } catch (error) {
    console.error('Error scraping device:', error);
    return NextResponse.json(
      { error: 'Failed to scrape device' },
      { status: 500 }
    );
  }
}

async function searchAccessories(query: string, type?: string) {
  try {
    // Map accessory types to Amazon categories
    const categoryMap: Record<string, string> = {
      'tws': 'electronics',
      'ssd': 'computers',
      'charger': 'electronics',
      'cable': 'electronics',
      'monitor': 'computers',
      'soundbar': 'electronics'
    };

    const category = type ? categoryMap[type] : undefined;
    const productLinks = await AmazonScraper.searchProducts(query, category);
    
    return NextResponse.json({ links: productLinks });
  } catch (error) {
    console.error('Error searching accessories:', error);
    return NextResponse.json(
      { error: 'Failed to search accessories' },
      { status: 500 }
    );
  }
}

async function scrapeAccessory(url: string, type: string) {
  try {
    const productData = await AmazonScraper.scrapeProduct(url);
    
    if (!productData) {
      return NextResponse.json(
        { error: 'Failed to scrape accessory data' },
        { status: 404 }
      );
    }

    // Process based on accessory type
    let specs: any = {};
    let compatibility: any = {
      deviceTypes: ['smartphone', 'laptop', 'tablet'],
      requirements: {}
    };

    if (type === 'tws') {
      const twsSpecs = AmazonScraper.extractTWSSpecs(productData);
      specs = {
        supportedCodecs: twsSpecs.supportedCodecs,
        features: twsSpecs.features
      };
      compatibility.requirements.minBluetoothVersion = '4.0';
    } else if (type === 'ssd') {
      specs = {
        storageInterface: 'NVMe', // Default, would need better extraction
        capacity: productData.specifications['Capacity'] || 'Unknown'
      };
      compatibility.deviceTypes = ['laptop'];
      compatibility.requirements.requiredPorts = ['NVMe'];
    }

    // Save to database
    await connectDB();
    
    const accessory = new Accessory({
      name: productData.name,
      brand: productData.brand,
      type: type as any,
      specs,
      compatibility,
      price: productData.price
    });

    await accessory.save();
    
    return NextResponse.json({ 
      message: 'Accessory scraped and saved successfully',
      accessory: productData 
    });
  } catch (error) {
    console.error('Error scraping accessory:', error);
    return NextResponse.json(
      { error: 'Failed to scrape accessory' },
      { status: 500 }
    );
  }
}
