{"c": ["app/layout", "app/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/class-variance-authority/dist/index.mjs", "(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cable.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/headphones.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/laptop.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/speaker.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tablet.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CWebProject%5C%5Cproductfinder%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs", "(app-pages-browser)/./src/app/page.tsx", "(app-pages-browser)/./src/components/AccessorySelector.tsx", "(app-pages-browser)/./src/components/CompatibilityResult.tsx", "(app-pages-browser)/./src/components/DeviceSelector.tsx", "(app-pages-browser)/./src/components/ui/Badge.tsx", "(app-pages-browser)/./src/components/ui/Button.tsx", "(app-pages-browser)/./src/components/ui/Card.tsx", "(app-pages-browser)/./src/lib/utils.ts"]}