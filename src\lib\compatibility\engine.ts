import { IDevice } from '../database/models/Device';
import { IAccessory } from '../database/models/Accessory';

export interface CompatibilityResult {
  compatible: boolean;
  compatibleFeatures: string[];
  incompatibleFeatures: string[];
  warnings: string[];
  suggestions: string[];
  score: number; // 0-100
}

export class CompatibilityEngine {
  static checkCompatibility(device: IDevice, accessory: IAccessory): CompatibilityResult {
    const result: CompatibilityResult = {
      compatible: false,
      compatibleFeatures: [],
      incompatibleFeatures: [],
      warnings: [],
      suggestions: [],
      score: 0
    };

    switch (accessory.type) {
      case 'tws':
        return this.checkTWSCompatibility(device, accessory, result);
      case 'ssd':
        return this.checkSSDCompatibility(device, accessory, result);
      case 'charger':
        return this.checkChargerCompatibility(device, accessory, result);
      case 'cable':
        return this.checkCableCompatibility(device, accessory, result);
      case 'monitor':
        return this.checkMonitorCompatibility(device, accessory, result);
      case 'soundbar':
        return this.checkSoundbarCompatibility(device, accessory, result);
      default:
        result.incompatibleFeatures.push('Unknown accessory type');
        return result;
    }
  }

  private static checkTWSCompatibility(
    device: IDevice, 
    accessory: IAccessory, 
    result: CompatibilityResult
  ): CompatibilityResult {
    const deviceCodecs = device.specs.supportedCodecs || [];
    const accessoryCodecs = accessory.specs.supportedCodecs || [];
    
    // Check Bluetooth version
    if (device.specs.bluetooth && accessory.compatibility.requirements.minBluetoothVersion) {
      const deviceBT = parseFloat(device.specs.bluetooth);
      const requiredBT = parseFloat(accessory.compatibility.requirements.minBluetoothVersion);
      
      if (deviceBT >= requiredBT) {
        result.compatibleFeatures.push(`Bluetooth ${device.specs.bluetooth} supported`);
      } else {
        result.incompatibleFeatures.push(`Requires Bluetooth ${requiredBT}, device has ${deviceBT}`);
      }
    }

    // Check codec compatibility
    const commonCodecs = deviceCodecs.filter(codec => accessoryCodecs.includes(codec));
    
    if (commonCodecs.length > 0) {
      result.compatibleFeatures.push(`Audio codecs: ${commonCodecs.join(', ')}`);
    }

    const unsupportedCodecs = accessoryCodecs.filter(codec => !deviceCodecs.includes(codec));
    if (unsupportedCodecs.length > 0) {
      result.incompatibleFeatures.push(`Unsupported codecs: ${unsupportedCodecs.join(', ')}`);
      result.warnings.push(`You won't get the best audio quality without ${unsupportedCodecs.join(', ')} support`);
    }

    // Calculate score
    const codecScore = (commonCodecs.length / accessoryCodecs.length) * 100;
    result.score = Math.round(codecScore);
    result.compatible = result.score > 50;

    return result;
  }

  private static checkSSDCompatibility(
    device: IDevice, 
    accessory: IAccessory, 
    result: CompatibilityResult
  ): CompatibilityResult {
    const deviceInterfaces = device.specs.storageInterface || [];
    const ssdInterface = accessory.specs.storageInterface;

    if (ssdInterface && deviceInterfaces.includes(ssdInterface)) {
      result.compatibleFeatures.push(`${ssdInterface} interface supported`);
      result.score = 100;
      result.compatible = true;
    } else {
      result.incompatibleFeatures.push(`${ssdInterface} interface not supported`);
      result.suggestions.push(`Your device supports: ${deviceInterfaces.join(', ')}`);
      result.score = 0;
    }

    return result;
  }

  private static checkChargerCompatibility(
    device: IDevice, 
    accessory: IAccessory, 
    result: CompatibilityResult
  ): CompatibilityResult {
    const devicePD = device.specs.powerDelivery;
    const chargerPD = accessory.specs.powerDelivery;

    if (device.specs.usbType && accessory.specs.cableType) {
      if (device.specs.usbType === accessory.specs.cableType) {
        result.compatibleFeatures.push(`${device.specs.usbType} connector compatible`);
      } else {
        result.incompatibleFeatures.push(`Connector mismatch: ${device.specs.usbType} vs ${accessory.specs.cableType}`);
      }
    }

    if (devicePD && chargerPD) {
      const deviceWatts = parseInt(devicePD);
      const chargerWatts = parseInt(chargerPD);
      
      if (chargerWatts >= deviceWatts) {
        result.compatibleFeatures.push(`Power delivery: ${chargerPD} (sufficient)`);
      } else {
        result.warnings.push(`Charger provides ${chargerPD}, device needs ${devicePD}`);
      }
    }

    result.score = result.compatibleFeatures.length > 0 ? 85 : 20;
    result.compatible = result.score > 50;

    return result;
  }

  private static checkCableCompatibility(
    device: IDevice, 
    accessory: IAccessory, 
    result: CompatibilityResult
  ): CompatibilityResult {
    // Similar to charger but focus on data transfer capabilities
    if (device.specs.usbType === accessory.specs.cableType) {
      result.compatibleFeatures.push(`${device.specs.usbType} connector compatible`);
      result.score = 90;
      result.compatible = true;
    } else {
      result.incompatibleFeatures.push(`Connector mismatch`);
      result.score = 0;
    }

    return result;
  }

  private static checkMonitorCompatibility(
    device: IDevice, 
    accessory: IAccessory, 
    result: CompatibilityResult
  ): CompatibilityResult {
    const devicePorts = device.specs.displayPorts || [];
    const monitorInputs = accessory.compatibility.requirements.requiredPorts || [];

    const commonPorts = devicePorts.filter(port => monitorInputs.includes(port));
    
    if (commonPorts.length > 0) {
      result.compatibleFeatures.push(`Display ports: ${commonPorts.join(', ')}`);
    } else {
      result.incompatibleFeatures.push(`No compatible display ports`);
    }

    // Check refresh rate support
    const deviceRefreshRates = device.specs.refreshRateSupport || [60];
    const monitorRefreshRates = accessory.specs.refreshRate || [60];
    
    const maxDeviceRate = Math.max(...deviceRefreshRates);
    const maxMonitorRate = Math.max(...monitorRefreshRates);
    
    if (maxDeviceRate >= maxMonitorRate) {
      result.compatibleFeatures.push(`Refresh rate: up to ${maxMonitorRate}Hz supported`);
    } else {
      result.warnings.push(`Monitor supports ${maxMonitorRate}Hz, device limited to ${maxDeviceRate}Hz`);
    }

    result.score = commonPorts.length > 0 ? 80 : 10;
    result.compatible = result.score > 50;

    return result;
  }

  private static checkSoundbarCompatibility(
    device: IDevice, 
    accessory: IAccessory, 
    result: CompatibilityResult
  ): CompatibilityResult {
    // Check HDMI ARC/eARC support
    if (device.specs.hdmiVersion && accessory.specs.hdmiVersion) {
      result.compatibleFeatures.push(`HDMI ${device.specs.hdmiVersion} compatible`);
    }

    // Check audio format support
    const deviceCodecs = device.specs.supportedCodecs || [];
    const soundbarFormats = accessory.specs.audioSupport || [];
    
    const commonFormats = deviceCodecs.filter(codec => soundbarFormats.includes(codec));
    
    if (commonFormats.length > 0) {
      result.compatibleFeatures.push(`Audio formats: ${commonFormats.join(', ')}`);
    }

    result.score = result.compatibleFeatures.length > 0 ? 75 : 30;
    result.compatible = result.score > 50;

    return result;
  }
}
