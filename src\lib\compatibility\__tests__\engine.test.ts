import { CompatibilityEngine } from '../engine';
import { IDevice } from '../../database/models/Device';
import { IAccessory } from '../../database/models/Accessory';

// Mock device data
const mockDevice: Partial<IDevice> = {
  name: 'iPhone 15 Pro',
  brand: 'Apple',
  type: 'smartphone',
  specs: {
    bluetooth: '5.3',
    supportedCodecs: ['AAC', 'SBC'],
    usbType: 'Type-C',
    powerDelivery: '20W'
  }
};

// Mock accessory data
const mockTWSAccessory: Partial<IAccessory> = {
  name: 'AirPods Pro 2',
  brand: 'Apple',
  type: 'tws',
  specs: {
    supportedCodecs: ['AAC', 'SBC']
  },
  compatibility: {
    deviceTypes: ['smartphone'],
    requirements: {
      minBluetoothVersion: '5.0'
    }
  }
};

const mockIncompatibleTWS: Partial<IAccessory> = {
  name: 'Sony WF-1000XM4',
  brand: 'Sony',
  type: 'tws',
  specs: {
    supportedCodecs: ['LDAC', 'AAC']
  },
  compatibility: {
    deviceTypes: ['smartphone'],
    requirements: {
      minBluetoothVersion: '5.0'
    }
  }
};

describe('CompatibilityEngine', () => {
  describe('TWS Compatibility', () => {
    it('should return high compatibility for matching codecs', () => {
      const result = CompatibilityEngine.checkCompatibility(
        mockDevice as IDevice,
        mockTWSAccessory as IAccessory
      );

      expect(result.compatible).toBe(true);
      expect(result.score).toBeGreaterThan(50);
      expect(result.compatibleFeatures).toContain('Audio codecs: AAC, SBC');
    });

    it('should return lower compatibility for missing codecs', () => {
      const result = CompatibilityEngine.checkCompatibility(
        mockDevice as IDevice,
        mockIncompatibleTWS as IAccessory
      );

      expect(result.score).toBeLessThan(100);
      expect(result.incompatibleFeatures).toContain('Unsupported codecs: LDAC');
      expect(result.warnings.length).toBeGreaterThan(0);
    });

    it('should check Bluetooth version compatibility', () => {
      const result = CompatibilityEngine.checkCompatibility(
        mockDevice as IDevice,
        mockTWSAccessory as IAccessory
      );

      expect(result.compatibleFeatures).toContain('Bluetooth 5.3 supported');
    });
  });

  describe('SSD Compatibility', () => {
    const mockLaptop: Partial<IDevice> = {
      name: 'MacBook Pro',
      brand: 'Apple',
      type: 'laptop',
      specs: {
        storageInterface: ['NVMe', 'SATA']
      }
    };

    const mockSSD: Partial<IAccessory> = {
      name: '980 PRO',
      brand: 'Samsung',
      type: 'ssd',
      specs: {
        storageInterface: 'NVMe'
      }
    };

    it('should return full compatibility for matching interface', () => {
      const result = CompatibilityEngine.checkCompatibility(
        mockLaptop as IDevice,
        mockSSD as IAccessory
      );

      expect(result.compatible).toBe(true);
      expect(result.score).toBe(100);
      expect(result.compatibleFeatures).toContain('NVMe interface supported');
    });
  });

  describe('Charger Compatibility', () => {
    const mockCharger: Partial<IAccessory> = {
      name: 'USB-C Charger',
      brand: 'Generic',
      type: 'charger',
      specs: {
        cableType: 'Type-C',
        powerDelivery: '25W'
      }
    };

    it('should check connector and power compatibility', () => {
      const result = CompatibilityEngine.checkCompatibility(
        mockDevice as IDevice,
        mockCharger as IAccessory
      );

      expect(result.compatibleFeatures).toContain('Type-C connector compatible');
      expect(result.score).toBeGreaterThan(50);
    });
  });
});

// Note: To run these tests, you would need to install Jest and configure it
// npm install --save-dev jest @types/jest ts-jest
// Add to package.json scripts: "test": "jest"
